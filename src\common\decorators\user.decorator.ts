import { AllUserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const User = createParamDecorator((data: keyof AllUserJwtInfo | undefined, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  const user: AllUserJwtInfo | undefined = request.user;

  // If data is provided, return the specific property of the user object
  return data ? user?.[data] : user;
});
