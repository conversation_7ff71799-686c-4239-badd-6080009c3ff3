import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateIngredientDto } from './dtos/create-ingredient.dto';
import { ListIngredientDto } from './dtos/list-ingredient.dto';
import { UpdateIngredientDto } from './dtos/update-ingredient.dto';
import { Ingredient } from './entities/ingredient.entity';
import { IngredientsService } from './ingredients.service';

@ApiTags('Ingredients')
@Controller('ingredients')
@Roles({ userType: UserType.MERCHANT_USER })
export class IngredientsController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];
  constructor(private readonly ingredientsService: IngredientsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if ingredient name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.ingredientsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(@Body() createIngredientDto: CreateIngredientDto, @User() user: UserMerchantJwtInfo): Promise<Ingredient> {
    return this.ingredientsService.create(createIngredientDto, user, this.permissionRoles);
  }

  @Get()
  findAll(
    @Query() listIngredientDto: ListIngredientDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Pagination<Ingredient>> {
    return this.ingredientsService.findAll(listIngredientDto, user, this.permissionRoles);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Ingredient> {
    return this.ingredientsService.findOne(id, user, this.permissionRoles);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateIngredientDto: UpdateIngredientDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Ingredient> {
    return this.ingredientsService.update(id, updateIngredientDto, user, this.permissionRoles);
  }
}
