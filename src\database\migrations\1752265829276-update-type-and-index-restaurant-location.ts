import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTypeAndIndexRestaurantLocation1752265829276 implements MigrationInterface {
  name = 'UpdateTypeAndIndexRestaurantLocation1752265829276';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "location"`);
      await queryRunner.query(`ALTER TABLE "restaurants" ADD "location" geography(Point,4326) NOT NULL`);
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_restaurants_location_geography" ON "restaurants" USING GIST ("location")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_restaurants_location_geography"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "location"`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "location" geometry(POINT,4326) NOT NULL`);
  }
}
