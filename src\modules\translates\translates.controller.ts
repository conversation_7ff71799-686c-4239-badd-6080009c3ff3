import { User } from '@/common/decorators/user.decorator';
import { Language } from '@/common/enums/language.enum';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Get, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { Public } from '../auth/decorators/public.decorator';
import { GetTranslatesDto } from './dtos/get-translates.dto';
import { UpdateTranslatesDto } from './dtos/update-translates.dto';
import { TranslateResponseItem, TranslatesService } from './translates.service';

@ApiTags('Translates')
@Controller('translates')
@Roles({ userType: UserType.MERCHANT_USER })
export class TranslatesController {
  constructor(private readonly translatesService: TranslatesService) {}
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];

  @Get()
  async getTranslates(
    @Query() dto: GetTranslatesDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<TranslateResponseItem[]> {
    return this.translatesService.getTranslates(dto, user, this.permissionRoles);
  }

  @Put()
  async updateTranslates(
    @Body() dto: UpdateTranslatesDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<{ updated: number }> {
    return this.translatesService.updateTranslates(dto, user, this.permissionRoles);
  }

  @Public()
  @Get('list-languages')
  getListLanguages() {
    return {
      languages: {
        [Language.EN]: 'English',
        [Language.VI]: 'Tiếng Việt',
      },

      fields: {
        publishedName: {
          [Language.EN]: 'publishedNameEn',
          [Language.VI]: 'publishedNameVi',
        },
        description: {
          [Language.EN]: 'descriptionEn',
          [Language.VI]: 'descriptionVi',
        },
      },
    };
  }
}
