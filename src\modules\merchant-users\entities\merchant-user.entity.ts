import { Exclude } from 'class-transformer';
import { Column, Entity, Index, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { MerchantUserPermission } from './merchant-user-permission.entity';

@Entity('merchant_users')
export class MerchantUser extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  email: string;

  @Column({ name: 'first_name', type: 'varchar' })
  firstName: string;

  @Column({ name: 'last_name', type: 'varchar' })
  lastName: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar', nullable: true })
  phone: string | null;

  @Column({ type: 'varchar' })
  @Exclude()
  password: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ type: 'boolean', default: false })
  isSuperAdmin: boolean;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt: Date | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ default: false })
  banned: boolean;

  // One user can have multiple permissions
  @OneToMany(() => MerchantUserPermission, (permission) => permission.merchantUser)
  permissions: WrapperType<MerchantUserPermission>[];
}
