import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MerchantStaff } from '@/modules/merchant-staff/entities/merchant-staff.entity';

import { RestaurantReview } from './restaurant-review.entity';

@Entity('restaurant_review_replies')
@Index(['restaurantReviewId', 'createdAt'], { where: 'deleted_at IS NULL' })
export class RestaurantReviewReply extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_review_id', type: 'uuid' })
  restaurantReviewId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'merchant_staff_id', type: 'uuid', nullable: true })
  merchantStaffId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'user_id', type: 'uuid', nullable: true })
  userId: string;

  @Column({ type: 'text' })
  comment: string;

  @ManyToOne(() => RestaurantReview)
  @JoinColumn({ name: 'restaurant_review_id' })
  restaurantReview?: WrapperType<RestaurantReview>;

  @ManyToOne(() => MerchantStaff)
  @JoinColumn({ name: 'merchant_staff_id' })
  merchantStaff?: WrapperType<MerchantStaff>;
}
