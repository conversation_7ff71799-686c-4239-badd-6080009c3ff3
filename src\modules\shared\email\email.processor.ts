import { Job } from 'bullmq';
import * as crypto from 'crypto';
import { Resend } from 'resend';

import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { TemplateService } from './template.service';

export interface SendOtpEmailJob {
  to: string;
  code: string;
  otpExpiryMinutes: number;
  locale?: string;
}

@Processor('email')
export class EmailProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailProcessor.name);
  private readonly resend: Resend;
  private readonly defaultFromEmail: string;

  constructor(
    private configService: ConfigService,
    private templateService: TemplateService,
  ) {
    super();
    const apiKey = this.configService.get<string>('email.resendApiKey');
    if (!apiKey) {
      this.logger.warn('Resend API Key not configured. Email sending disabled.');
    } else {
      this.resend = new Resend(apiKey);
    }
    this.defaultFromEmail = this.configService.get<string>(
      'email.defaultFromEmail',
      '<EMAIL>', // Fallback default
    );
  }

  async process(job: Job<SendOtpEmailJob>): Promise<void> {
    const { to, code, otpExpiryMinutes, locale = 'en' } = job.data;

    try {
      this.logger.log(`Processing email job ${job.id} for ${to}`);
      await this.sendOtpEmail(to, code, otpExpiryMinutes, locale);
      this.logger.log(`Email job ${job.id} completed successfully for ${to}`);
    } catch (error) {
      this.logger.error(`Email job ${job.id} failed for ${to}:`, error);
      throw error; // Let BullMQ handle retries
    }
  }

  private async sendOtpEmail(to: string, code: string, otpExpiryMinutes: number, locale: string = 'en'): Promise<void> {
    if (!this.resend) {
      this.logger.error('Resend client not initialized. Cannot send email.');
      // Potentially throw an error or just return depending on desired behavior
      return;
    }

    const templateName = `email-otp-${locale}`; // e.g., email-otp-en
    const subject = locale === 'vi' ? 'Mã OTP Anh Béo của bạn' : 'Your Anh Beo OTP Code';

    try {
      const html = this.templateService.render(templateName, { code, otpExpiryMinutes });
      const idempotencyKey = this.generateIdempotencyKey(to, templateName);

      const { data, error } = await this.resend.emails.send({
        from: this.defaultFromEmail,
        to: [to],
        subject: subject,
        html: html,
        headers: {
          'X-Entity-Ref-ID': idempotencyKey, // Use Resend's idempotency header
        },
      });

      if (error) {
        this.logger.error(`Failed to send OTP email to ${to}: ${error.message}`, error);
        // Handle error appropriately (e.g., throw specific exception)
        throw new Error(`Failed to send email: ${error.message}`);
      } else {
        this.logger.log(`OTP email sent successfully to ${to}. Resend ID: ${data?.id}`);
      }
    } catch (error) {
      // Catch errors from template rendering or key generation too
      this.logger.error(`Error preparing or sending OTP email to ${to}: ${error.message}`, error.stack);
      throw error; // Re-throw or handle
    }
  }

  private generateIdempotencyKey(email: string, template: string): string {
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const hash = crypto.createHash('sha256');
    hash.update(`${email}-${template}-${date}`);
    return hash.digest('hex');
  }
}
