import { Repository } from 'typeorm';

import { UserType } from '@/modules/auth/enums/user-type.enum';
import { AdminJwtInfo, UserMerchantJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { MerchantUserPermission } from '@/modules/merchant-users/entities/merchant-user-permission.entity';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class PermissionMerchantUserService {
  constructor(
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(MerchantUserPermission)
    private merchantUserPermissionRepository: Repository<MerchantUserPermission>,
  ) {}

  async getPermissionList(user: UserMerchantJwtInfo, roles?: MerchantUserRole[]) {
    const queryBuilder = this.merchantUserPermissionRepository
      .createQueryBuilder('permission')
      .select('permission.id, permission.restaurantId, permission.brandId')
      .where('permission.merchantUserId = :userId', { userId: user.id });
    if (roles) {
      queryBuilder.andWhere('permission.role IN (:...roles)', { roles });
    }

    const permissions = await queryBuilder.getMany();

    const restaurantIds = permissions.map((permission) => permission.restaurantId).filter((id) => id !== null);
    const brandIds = permissions.map((permission) => permission.brandId).filter((id) => id !== null);

    if (brandIds.length) {
      const listRestaurant = await this.restaurantRepository
        .createQueryBuilder('restaurant')
        .select('restaurant.id')
        .where('restaurant.brandId IN (:...brandIds)', { brandIds })
        .getMany();
      const listRestaurantIds = listRestaurant.map((restaurant) => restaurant.id);
      if (listRestaurantIds.length) {
        restaurantIds.push(...listRestaurantIds);
      }
    }

    return [...new Set(restaurantIds)];
  }

  async getPermissionListByShops(
    user: UserMerchantJwtInfo,
    roles: MerchantUserRole[],
    { restaurantId, brandId }: { restaurantId?: string; brandId: string },
  ) {
    const queryBuilder = this.merchantUserPermissionRepository
      .createQueryBuilder('permission')
      .where('permission.merchantUserId = :userId', { userId: user.id });
    if (roles) {
      queryBuilder.andWhere('permission.role IN (:...roles)', { roles });
    }
    if (restaurantId) {
      queryBuilder.andWhere('permission.restaurantId = :restaurantId OR permission.brandId = :brandId', {
        restaurantId,
        brandId,
      });
    } else {
      queryBuilder.andWhere('permission.brandId = :brandId', { brandId });
    }

    return queryBuilder.getMany();
  }

  async checkPermission(
    user: UserMerchantJwtInfo | AdminJwtInfo,
    roles: MerchantUserRole[],
    params: { restaurantId?: string; brandId: string },
  ) {
    if (user.userType === UserType.AB_ADMIN) return true;

    if (user.isSuperAdmin) return true;
    if (!roles.length) return false;

    const permissions = await this.getPermissionListByShops(user, roles, params);
    return permissions.some((permission) => roles.includes(permission.role));
  }

  /**
   * Verify that a restaurant exists and the user has access to it
   * @param restaurantId The ID of the restaurant to verify access to
   * @param user The user to verify access for
   * @throws NotFoundException if the restaurant doesn't exist or the user doesn't have access to it
   */
  async verifyAccessRestaurant(
    restaurantId: string,
    user: UserMerchantJwtInfo | AdminJwtInfo,
    roles: MerchantUserRole[] = [],
  ) {
    const queryBuilder = this.restaurantRepository
      .createQueryBuilder('restaurant')
      .where('restaurant.id = :restaurantId', { restaurantId })
      .innerJoin('restaurant.brand', 'brand')
      .select(['restaurant.id', 'brand.id']);

    const restaurant = await queryBuilder.getOne();
    if (!restaurant) {
      throw new NotFoundException(`Restaurant not found`);
    }

    const hasPermission = await this.checkPermission(user, roles, {
      restaurantId: restaurant.id,
      brandId: restaurant.brand.id,
    });

    if (!restaurant || !hasPermission) {
      throw new ForbiddenException(`You don't have access to this restaurant`);
    }

    return restaurant;
  }

  /**
   * Verify that a brand exists and the user has access to it
   * @param brandId The ID of the brand to verify access to
   * @param user The user to verify access for
   * @throws NotFoundException if the brand doesn't exist or the user doesn't have access to it
   */
  async verifyAccessBrand(
    brandId: string,
    user: UserMerchantJwtInfo | AdminJwtInfo,
    roles: MerchantUserRole[] = [],
  ): Promise<void> {
    const queryBuilder = this.brandRepository
      .createQueryBuilder('brand')
      .where('brand.id = :brandId', { brandId })
      .select(['brand.id']);

    const brand = await queryBuilder.getOne();
    if (!brand) {
      throw new NotFoundException(`Brand not found`);
    }

    const hasPermission = await this.checkPermission(user, roles, {
      brandId: brand.id,
    });

    if (!brand || !hasPermission) {
      throw new ForbiddenException(`You don't have access to this brand`);
    }
  }
}
