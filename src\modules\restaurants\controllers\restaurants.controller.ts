import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { CheckNameExistsBrandDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { allMerchantUserRoles, MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CloneGeofencingDto } from '../dtos/clone-geofencing.dto';
import { CloneRestaurantDataDto } from '../dtos/clone-restaurant-data.dto';
import { CreateRestaurantDto } from '../dtos/create-restaurant.dto';
import { ListReconciliationDto } from '../dtos/list-reconciliation.dto';
import { ListRestaurantDto } from '../dtos/list-restaurant.dto';
import { UpdateRestaurantDto } from '../dtos/update-restaurant.dto';
import { Restaurant } from '../entities/restaurant.entity';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('Restaurants')
@Controller('restaurants')
@Roles({ userType: UserType.MERCHANT_USER })
export class RestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];
  private readonly permissionOnlySuperAdmin = [];

  @Post()
  create(@Body() createRestaurantDto: CreateRestaurantDto, @User() user: UserMerchantJwtInfo): Promise<Restaurant> {
    return this.restaurantsService.create(createRestaurantDto, user, this.permissionOnlySuperAdmin);
  }

  @Post('check-name-exists')
  async checkExists(@Body() dto: CheckNameExistsBrandDto): Promise<NameExistsResponseDto> {
    const exists = await this.restaurantsService.checkNameExists(
      dto.brandId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Get()
  findAll(
    @Query() listRestaurantDto: ListRestaurantDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Pagination<Restaurant>> {
    return this.restaurantsService.findAll(listRestaurantDto, user, allMerchantUserRoles);
  }

  @Get('reconciliations')
  findAllReconciliations(@Query() listRestaurantDto: ListReconciliationDto, @User() user: UserMerchantJwtInfo) {
    return this.restaurantsService.findAllReconciliations(listRestaurantDto, user, [
      MerchantUserRole.OWNER,
      MerchantUserRole.ADMIN,
      MerchantUserRole.ACCOUNTANT,
    ]);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Restaurant> {
    return this.restaurantsService.findOneRestaurant(id, user, allMerchantUserRoles);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRestaurantDto: UpdateRestaurantDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Restaurant> {
    return this.restaurantsService.update(id, updateRestaurantDto, user, this.permissionRoles);
  }

  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Restaurant> {
    return this.restaurantsService.activate(id, user, this.permissionOnlySuperAdmin);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Restaurant> {
    return this.restaurantsService.deactivate(id, user, this.permissionOnlySuperAdmin);
  }

  @Delete(':id')
  delete(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Restaurant> {
    return this.restaurantsService.softDelete(id, user, this.permissionOnlySuperAdmin);
  }

  @Post('clone-menu')
  cloneMenu(@Body() cloneDto: CloneRestaurantDataDto, @User() user: UserMerchantJwtInfo) {
    return this.restaurantsService.cloneDataMenu(
      cloneDto.sourceRestaurantId,
      cloneDto.targetRestaurantId,
      user,
      this.permissionOnlySuperAdmin,
    );
  }

  @Post('clone-geofencing')
  async cloneGeofencing(@Body() cloneDto: CloneGeofencingDto, @User() user: UserMerchantJwtInfo) {
    return this.restaurantsService.cloneGeofencing(
      cloneDto.sourceRestaurantId,
      cloneDto.targetRestaurantId,
      user,
      this.permissionOnlySuperAdmin,
    );
  }
}
