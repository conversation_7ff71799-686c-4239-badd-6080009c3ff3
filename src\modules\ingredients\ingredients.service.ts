import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { NameValidationHelper } from '@/common/helpers/name-validation.helper';
import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateIngredientDto } from './dtos/create-ingredient.dto';
import { ListIngredientDto } from './dtos/list-ingredient.dto';
import { UpdateIngredientDto } from './dtos/update-ingredient.dto';
import { Ingredient } from './entities/ingredient.entity';

@Injectable()
export class IngredientsService {
  constructor(
    @InjectRepository(Ingredient)
    private readonly ingredientRepository: Repository<Ingredient>,
    private readonly permissionMerchantUserService: PermissionMerchantUserService,
    private readonly dataSource: DataSource,
  ) {}

  async create(
    createIngredientDto: CreateIngredientDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Ingredient> {
    await this.permissionMerchantUserService.verifyAccessRestaurant(
      createIngredientDto.restaurantId,
      user,
      permissionRoles,
    );

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      createIngredientDto.restaurantId,
      createIngredientDto.internalName,
      createIngredientDto.publishedName,
      undefined,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemIds from DTO if present
    const { menuItemIds, ...ingredientData } = createIngredientDto;

    let id: string = '';

    await this.dataSource.transaction(async (entityManager) => {
      // Create ingredient
      const ingredient = entityManager.create(Ingredient, ingredientData);
      const savedIngredient = await entityManager.save(ingredient);

      // Handle menu item relationships if menuItemIds are provided
      await this.handleMenuItemRelationship(savedIngredient, menuItemIds, entityManager);

      id = savedIngredient.id;
    });

    return this.findOneById(id);
  }

  async findAll(
    listIngredientDto: ListIngredientDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Pagination<Ingredient>> {
    const { internalName, publishedName, restaurantId, page, limit } = listIngredientDto;

    const queryBuilder = this.ingredientRepository.createQueryBuilder('ingredient');

    if (restaurantId) {
      queryBuilder.andWhere('ingredient.restaurantId = :restaurantId', { restaurantId });
    }

    // For merchant users, only show ingredients they have access to
    if (user.userType === UserType.MERCHANT_USER && !user.isSuperAdmin) {
      const restaurantIds = await this.permissionMerchantUserService.getPermissionList(user, permissionRoles);
      if (restaurantIds.length > 0) {
        queryBuilder
          .leftJoin('ingredient.restaurant', 'restaurant')
          .andWhere('restaurant.id IN (:...restaurantIds)', { restaurantIds });
      } else {
        // Không có quyền với restaurant nào, trả về kết quả rỗng
        queryBuilder.andWhere('1 = 0');
      }
    }

    // Apply filters
    if (internalName) {
      queryBuilder.andWhere('ingredient.internalName ILIKE :internalName', { internalName: `%${internalName}%` });
    }

    if (publishedName) {
      queryBuilder.andWhere('ingredient.publishedName ILIKE :publishedName', { publishedName: `%${publishedName}%` });
    }

    // Order by most recently updated
    queryBuilder.orderBy('ingredient.updatedAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<Ingredient> {
    const ingredient = await this.findOneById(id);

    await this.permissionMerchantUserService.verifyAccessRestaurant(ingredient.restaurantId, user, permissionRoles);

    return ingredient;
  }

  async findOneById(id: string): Promise<Ingredient> {
    const ingredient = await this.ingredientRepository.findOne({
      where: { id },
      relations: ['restaurant', 'menuItems'],
    });
    if (!ingredient) {
      throw new NotFoundException(`Ingredient not found`);
    }

    return ingredient;
  }

  async update(
    id: string,
    updateIngredientDto: UpdateIngredientDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Ingredient> {
    const ingredient = await this.findOne(id, user, permissionRoles);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      ingredient.restaurantId,
      updateIngredientDto.internalName,
      updateIngredientDto.publishedName,
      id,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemIds from DTO if present
    const { menuItemIds, ...ingredientData } = updateIngredientDto;

    // Update basic properties
    Object.assign(ingredient, ingredientData);

    await this.dataSource.transaction(async (entityManager) => {
      // Handle menu item relationships if menuItemIds are provided
      await this.handleMenuItemRelationship(ingredient, menuItemIds, entityManager);

      // Save the updated entity
      await entityManager.save(ingredient);
    });

    return this.findOneById(id);
  }

  /**
   * Helper method to handle ingredient relationships for entities
   * @param entity The entity to update (must have restaurantId and ingredients properties)
   * @param ingredientIds Array of ingredient IDs to link, or undefined to skip updating
   */
  async handleIngredientRelationship<T extends { restaurantId: string; ingredients?: Ingredient[] }>(
    entity: T,
    ingredientIds?: string[],
  ): Promise<void> {
    // If ingredientIds is undefined, don't update ingredients
    if (ingredientIds === undefined) {
      return;
    }

    // If ingredientIds is an empty array, clear all ingredients
    if (ingredientIds.length === 0) {
      entity.ingredients = [];
      return;
    }

    // Find ingredients that match both the provided IDs and the restaurant ID
    const ingredients = await this.ingredientRepository.find({
      where: {
        id: In(ingredientIds),
        restaurantId: entity.restaurantId,
      },
    });

    // Check if all ingredient IDs exist and belong to the restaurant
    if (ingredients.length !== ingredientIds.length) {
      const foundIds = ingredients.map((ingredient) => ingredient.id);
      const missingIds = ingredientIds.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following ingredient IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Set the ingredients relation
    entity.ingredients = ingredients;
  }

  async checkNameExists(
    restaurantId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      return NameValidationHelper.checkNameExists(
        this.ingredientRepository,
        'ingredient',
        restaurantId,
        'internalName',
        internalName,
        excludeId,
      );
    }

    if (publishedName) {
      return NameValidationHelper.checkNameExists(
        this.ingredientRepository,
        'ingredient',
        restaurantId,
        'publishedName',
        publishedName,
        excludeId,
      );
    }

    return false;
  }

  /**
   * Helper method to handle menu item relationships for ingredients (add ingredient to menu items)
   * @param ingredient The ingredient entity to update
   * @param menuItemIds Array of menu item IDs to link, or undefined to skip updating
   */
  private async handleMenuItemRelationship(
    ingredient: Ingredient,
    menuItemIds: string[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemIds is undefined, don't update menu items
    if (menuItemIds === undefined) {
      return;
    }

    if (!ingredient.id) {
      return;
    }

    // If menuItemIds is an empty array, clear all menu item relationships
    if (menuItemIds.length === 0) {
      ingredient.menuItems = [];
      return;
    }

    // Verify all menu item IDs exist and belong to the same restaurant
    const validMenuItems = await entityManager.find(MenuItem, {
      where: { id: In(menuItemIds), restaurantId: ingredient.restaurantId },
      select: ['id'],
    });

    if (validMenuItems.length !== menuItemIds.length) {
      const foundIds = validMenuItems.map((menuItem) => menuItem.id);
      const missingIds = menuItemIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `The following menu item IDs do not exist or don't belong to this restaurant: ${missingIds.join(', ')}`,
      );
    }

    // Set the menu items relation - TypeORM will handle the join table
    ingredient.menuItems = validMenuItems;
  }
}
