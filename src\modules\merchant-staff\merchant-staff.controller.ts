import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Get, Param, ParseUUIDPipe, Patch, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { ListMerchantStaffDto } from './dtos/list-merchant-staff.dto';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import { MerchantStaffService } from './merchant-staff.service';

@ApiTags('Merchant Staff')
@Controller('merchant-staff')
@Roles({ userType: UserType.MERCHANT_USER })
export class MerchantStaffController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN];
  constructor(private readonly merchantStaffService: MerchantStaffService) {}

  @Get()
  findAll(@Query() listMerchantStaffDto: ListMerchantStaffDto, @User() user: UserMerchantJwtInfo) {
    return this.merchantStaffService.findAll(listMerchantStaffDto, user, this.permissionRoles);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @User() user: UserMerchantJwtInfo) {
    return this.merchantStaffService.findOne(id, user, this.permissionRoles);
  }

  @Patch(':id/change-password')
  updatePassword(
    @Param('id') id: string,
    @Body() updatePasswordDto: UpdatePasswordDto,
    @User() user: UserMerchantJwtInfo,
  ) {
    return this.merchantStaffService.updatePassword(id, updatePasswordDto, user, this.permissionRoles);
  }

  @Put('ban/:id')
  ban(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo) {
    return this.merchantStaffService.ban(id, user, this.permissionRoles);
  }

  @Put('unban/:id')
  unban(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo) {
    return this.merchantStaffService.unban(id, user, this.permissionRoles);
  }
}
