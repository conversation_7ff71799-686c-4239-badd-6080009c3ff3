import { MerchantUserPermissionsService } from '@/modules/merchant-users/merchant-user-permissions.service';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MerchantUserPermission } from './entities/merchant-user-permission.entity';
import { MerchantUser } from './entities/merchant-user.entity';
import { MerchantUsersController } from './merchant-users.controller';
import { MerchantUsersService } from './merchant-users.service';

@Module({
  imports: [TypeOrmModule.forFeature([MerchantUser, MerchantUserPermission])],
  controllers: [MerchantUsersController],
  providers: [MerchantUsersService, MerchantUserPermissionsService],
  exports: [MerchantUsersService, MerchantUserPermissionsService],
})
export class MerchantUsersModule {}
