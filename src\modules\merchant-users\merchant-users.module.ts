import { Brand } from '@/modules/brands/entities/brand.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MerchantUserPermission } from './entities/merchant-user-permission.entity';
import { MerchantUser } from './entities/merchant-user.entity';
import { MerchantSignupController } from './merchant-signup.controller';
import { MerchantUserPermissionsService } from './merchant-user-permissions.service';
import { MerchantUsersController } from './merchant-users.controller';
import { MerchantUsersService } from './merchant-users.service';

@Module({
  imports: [TypeOrmModule.forFeature([MerchantUser, MerchantUserPermission, Brand, Restaurant])],
  controllers: [MerchantUsersController, MerchantSignupController],
  providers: [MerchantUsersService, MerchantUserPermissionsService],
  exports: [MerchantUsersService, MerchantUserPermissionsService],
})
export class MerchantUsersModule {}
