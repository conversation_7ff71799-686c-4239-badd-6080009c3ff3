import { IsEmail, <PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString, Length } from 'class-validator';

import { RemoveLeadingZeroPhone, ToLowerCase } from '@/common/decorators/transforms.decorator';
import { Language } from '@/common/enums/language.enum';
import { IsPhoneCountryCode } from '@/common/validators/phone-country-code.validator';
import { IsPhoneNumber } from '@/common/validators/phone-number.validator';
import { ApiProperty } from '@nestjs/swagger';

export class SendPhoneOtpDto {
  @ApiProperty({ example: '987654321', description: 'Phone number without country code' })
  @IsPhoneNumber()
  @IsNotEmpty()
  @RemoveLeadingZeroPhone()
  phone: string;

  @ApiProperty({ example: '+84' })
  @IsPhoneCountryCode()
  @IsNotEmpty()
  phoneCountryCode: string;
}

export class VerifyPhoneOtpDto {
  @ApiProperty({ example: '987654321', description: 'Phone number without country code' })
  @IsPhoneNumber()
  @IsNotEmpty()
  @RemoveLeadingZeroPhone()
  phone: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otp: string;

  @ApiProperty({ example: '+84' })
  @IsPhoneCountryCode()
  @IsNotEmpty()
  phoneCountryCode: string;
}

export class SendEmailOtpDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ example: 'verification_token_from_phone_step' })
  @IsString()
  @IsNotEmpty()
  phoneVerificationToken: string;
}

export class VerifyEmailOtpDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  @ToLowerCase()
  email: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otp: string;

  @ApiProperty({ example: 'verification_token_from_phone_step' })
  @IsString()
  @IsNotEmpty()
  phoneVerificationToken: string;
}

export class UpdateProfileDto {
  @ApiProperty({ example: 'John', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  firstName?: string;

  @ApiProperty({ example: 'Doe', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  lastName?: string;

  @ApiProperty({ enum: Language, example: Language.EN, required: false })
  @IsOptional()
  @IsEnum(Language)
  language?: Language;
}

export class FakeLoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty()
  email: string;
}
