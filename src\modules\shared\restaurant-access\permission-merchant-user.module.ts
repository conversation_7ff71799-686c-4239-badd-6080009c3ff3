import { Brand } from '@/modules/brands/entities/brand.entity';
import { MerchantUserPermission } from '@/modules/merchant-users/entities/merchant-user-permission.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { PermissionMerchantUserService } from './permission-merchant-user.service';

@Global() // Make services available globally
@Module({
  imports: [TypeOrmModule.forFeature([Restaurant, Brand, MerchantUserPermission])],
  providers: [PermissionMerchantUserService],
  exports: [PermissionMerchantUserService],
})
export class PermissionMerchantUserModule {}
