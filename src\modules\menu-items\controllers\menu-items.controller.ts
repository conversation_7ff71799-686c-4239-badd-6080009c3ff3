import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateMenuItemDto } from '../dtos/create-menu-item.dto';
import { ListMenuItemDto } from '../dtos/list-menu-item.dto';
import { UpdateMenuItemDto } from '../dtos/update-menu-item.dto';
import { MenuItem } from '../entities/menu-item.entity';
import { MenuItemsService } from '../menu-items.service';

@ApiTags('Menu Items')
@Controller('menu-items')
@Roles({ userType: UserType.MERCHANT_USER })
export class MenuItemsController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];
  constructor(private readonly menuItemsService: MenuItemsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu item name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menuItemsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(@Body() createMenuItemDto: CreateMenuItemDto, @User() user: UserMerchantJwtInfo): Promise<MenuItem> {
    return this.menuItemsService.create(createMenuItemDto, user, this.permissionRoles);
  }

  @Get()
  findAll(@Query() listMenuItemDto: ListMenuItemDto, @User() user: UserMerchantJwtInfo): Promise<Pagination<MenuItem>> {
    return this.menuItemsService.findAll(listMenuItemDto, user, this.permissionRoles);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<MenuItem> {
    return this.menuItemsService.findOne(id, user, this.permissionRoles);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuItemDto: UpdateMenuItemDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<MenuItem> {
    return this.menuItemsService.update(id, updateMenuItemDto, user, this.permissionRoles);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete menu item by ID' })
  async delete(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo) {
    return this.menuItemsService.softDelete(id, user, this.permissionRoles);
  }
}
