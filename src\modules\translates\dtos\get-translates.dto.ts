import { IsEnum, IsOptional, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export enum TranslateType {
  MENU_SECTION = 'menu_section',
  MENU_ITEM = 'menu_item',
  MENU_ITEM_OPTION_GROUP = 'menu_item_option_group',
}

export class GetTranslatesDto {
  @ApiProperty({ description: 'Restaurant ID' })
  @IsUUID()
  restaurantId: string;

  @ApiProperty({
    description: 'Type of entity to get translations for',
    enum: TranslateType,
    required: false,
  })
  @IsOptional()
  @IsEnum(TranslateType)
  type?: TranslateType;

  @ApiProperty({ description: 'Menu ID', required: false })
  @IsOptional()
  @IsUUID()
  menuId?: string;
}
