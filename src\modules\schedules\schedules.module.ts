import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { MenuSection } from '@/modules/menu-sections/entities/menu-section.entity';
import { OnepayModule } from '@/modules/onepay/onepay.module';
import { Order } from '@/modules/orders/entities/order.entity';
import { Reconciliation } from '@/modules/reconciliations/entities/reconciliation.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MenuSchedulesService } from './services/menu-schedules.service';
import { OnepaySchedulesService } from './services/onepay-schedules.services';
import { OrderSchedulesService } from './services/order-schedules.service';
import { ReconciliationsSchedulesService } from './services/reconciliations-schedules.service';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([Order, MenuSection, MenuItem, Restaurant, Reconciliation]),
    OnepayModule,
  ],
  providers: [OrderSchedulesService, OnepaySchedulesService, MenuSchedulesService, ReconciliationsSchedulesService],
})
export class SchedulesModule {}
