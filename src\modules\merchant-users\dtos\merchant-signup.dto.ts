import { Type } from 'class-transformer';
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  <PERSON>,
  ValidateNested,
} from 'class-validator';

import { RemoveLeadingZeroPhone, ToBoolean, ToLowerCase } from '@/common/decorators/transforms.decorator';
import { IsPhoneNumber } from '@/common/validators/phone-number.validator';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { BusinessType } from '@/modules/brands/enums/business-type.enum';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

export class MerchantUserSignupDto {
  @ApiProperty({ description: 'First name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Last name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({ description: 'Phone number of the merchant user' })
  @IsNotEmpty()
  @IsPhoneNumber()
  @RemoveLeadingZeroPhone()
  phone: string;

  @ApiProperty({ description: 'Email of the merchant user' })
  @IsNotEmpty()
  @IsEmail()
  @ToLowerCase()
  email: string;

  @ApiProperty({ description: 'Password of the merchant user' })
  @IsNotEmpty()
  @IsString()
  password: string;
}

export class BrandSignupDto {
  @ApiProperty({ description: 'Brand name' })
  @IsNotEmpty()
  @IsString()
  brandName: string;

  @ApiProperty({ description: 'Business type', enum: BusinessType })
  @IsNotEmpty()
  @IsEnum(BusinessType)
  businessType: BusinessType;

  @ApiProperty({ description: 'Registered name of the business' })
  @IsNotEmpty()
  @IsString()
  registeredName: string;

  @ApiProperty({ description: 'Registration number of the business' })
  @IsNotEmpty()
  @IsString()
  registrationNumber: string;

  @ApiProperty({ description: 'Headquarter address of the business' })
  @IsNotEmpty()
  @IsString()
  headquarterAddress: string;

  @ApiProperty({ description: 'Company tax code' })
  @IsNotEmpty()
  @IsString()
  companyTaxCode: string;

  @ApiProperty({ description: 'Logo URL', required: false })
  @IsOptional()
  @IsString()
  @IsValidS3Url(FolderType.BRAND_LOGO)
  logoUrl?: string;
}

export class RestaurantSignupDto {
  @ApiProperty({ description: 'Avatar image URL of the restaurant' })
  @IsString()
  @IsValidS3Url([FolderType.RESTAURANT_AVATAR, FolderType.BRAND_LOGO])
  avatarImg: string;

  @ApiProperty({ description: 'Background image URL of the restaurant' })
  @IsString()
  @IsValidS3Url(FolderType.RESTAURANT_BANNER)
  backgroundImg: string;

  @ApiProperty({ description: 'Address of the restaurant', required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: 'Ward of the restaurant', required: false })
  @IsOptional()
  @IsString()
  ward?: string;

  @ApiProperty({ description: 'District of the restaurant', required: false })
  @IsOptional()
  @IsString()
  district?: string;

  @ApiProperty({ description: 'Province of the restaurant', required: false })
  @IsOptional()
  @IsString()
  province?: string;

  @ApiProperty({ description: 'Phone number of the restaurant', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Latitude of the restaurant location',
    example: 10.7769,
    minimum: -90,
    maximum: 90,
  })
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Type(() => Number)
  latitude: number;

  @ApiProperty({
    description: 'Longitude of the restaurant location',
    example: 106.7009,
    minimum: -180,
    maximum: 180,
  })
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Type(() => Number)
  longitude: number;
}

export class MerchantSignupDto {
  @ApiProperty({ description: 'Merchant user information', type: MerchantUserSignupDto })
  @ValidateNested()
  @Type(() => MerchantUserSignupDto)
  merchantUser: MerchantUserSignupDto;

  @ApiProperty({ description: 'Brand information', type: BrandSignupDto })
  @ValidateNested()
  @Type(() => BrandSignupDto)
  brand: BrandSignupDto;

  @ApiProperty({ description: 'Restaurant information', type: RestaurantSignupDto })
  @ValidateNested()
  @Type(() => RestaurantSignupDto)
  restaurant: RestaurantSignupDto;
}
