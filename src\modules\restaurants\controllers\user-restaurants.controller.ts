import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { OptionalAuth } from '@/modules/auth/decorators/optional-auth.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { FilterRestaurantByAddressDto, FilterRestaurantDto } from '../dtos/filter-restaurant.dto';
import { RestaurantSuggestionDto, SuggestionResponseDto } from '../dtos/restaurant-suggestion.dto';
import { Restaurant } from '../entities/restaurant.entity';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('(User) Restaurants')
@Controller('user/restaurants')
@Roles({ userType: UserType.USER, role: '*' })
@OptionalAuth()
export class UserRestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Get()
  userFindAll(
    @Query() filterRestaurantDto: FilterRestaurantDto,
    @User() user: UserJwtInfo,
  ): Promise<Pagination<Restaurant>> {
    return this.restaurantsService.userFindAll(filterRestaurantDto, user);
  }

  @Get(':id')
  userFindOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Query() filterRestaurantByAddressDto: FilterRestaurantByAddressDto,
    @User() user: UserJwtInfo,
  ): Promise<Restaurant> {
    return this.restaurantsService.userFindOne(id, filterRestaurantByAddressDto, user);
  }

  @Post('suggestions')
  async getSuggestions(@Body() dto: RestaurantSuggestionDto): Promise<SuggestionResponseDto> {
    const suggestions = await this.restaurantsService.getSuggestions(dto.search);
    return { suggestions };
  }
}
