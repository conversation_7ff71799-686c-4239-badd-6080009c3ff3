import { Modu<PERSON> } from '@nestjs/common';

import { EmailModule } from './email/email.module';
import { FCMModule } from './fcm/fcm.module';
import { MicroserviceModule } from './microservice/microservice.module';
import { RedisModule } from './redis/redis.module';
import { PermissionMerchantUserModule } from './restaurant-access/permission-merchant-user.module';
import { SmsModule } from './sms/sms.module';

@Module({
  imports: [EmailModule, FCMModule, MicroserviceModule, RedisModule, PermissionMerchantUserModule, SmsModule],
  exports: [EmailModule, FCMModule, MicroserviceModule, RedisModule, PermissionMerchantUserModule, SmsModule],
})
export class SharedModule {}
