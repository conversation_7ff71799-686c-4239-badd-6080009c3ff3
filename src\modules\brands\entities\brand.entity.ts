import { Column, Entity, Index, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MerchantUserPermission } from '@/modules/merchant-users/entities/merchant-user-permission.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { BusinessType } from '../enums/business-type.enum';

@Entity('brands')
export class Brand extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  name: string;

  @Column({ name: 'include_vat', default: false })
  includeVat: boolean;

  @Column({ name: 'logo_url', type: 'varchar', nullable: true })
  logoUrl: string | null;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'code', type: 'varchar' })
  code: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @Column({ name: 'business_type', type: 'enum', enum: BusinessType, nullable: true })
  businessType?: BusinessType | null;

  @Column({ name: 'registered_name', type: 'varchar', nullable: true })
  registeredName?: string | null;

  @Column({ name: 'registration_number', type: 'varchar', nullable: true })
  registrationNumber?: string | null;

  @Column({ name: 'headquarter_address', type: 'text', nullable: true })
  headquarterAddress?: string | null;

  @Column({ name: 'company_tax_code', type: 'varchar', nullable: true })
  companyTaxCode?: string | null;

  @OneToMany(() => Restaurant, (restaurant) => restaurant.brand)
  restaurants: Restaurant[];

  @OneToMany(() => MerchantUserPermission, (permission) => permission.brand)
  permissions: WrapperType<MerchantUserPermission>[];
}
