import { Column, Entity, Index, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MerchantUserPermission } from '@/modules/merchant-users/entities/merchant-user-permission.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

@Entity('brands')
export class Brand extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  name: string;

  @Column({ name: 'include_vat', default: false })
  includeVat: boolean;

  @Column({ name: 'logo_url', type: 'varchar', nullable: true })
  logoUrl: string | null;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'code', type: 'varchar' })
  code: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt?: Date | null;

  @OneToMany(() => Restaurant, (restaurant) => restaurant.brand)
  restaurants: Restaurant[];

  @OneToMany(() => MerchantUserPermission, (permission) => permission.brand)
  permissions: WrapperType<MerchantUserPermission>[];
}
