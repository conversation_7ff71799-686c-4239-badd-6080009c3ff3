import { IsBoolean, <PERSON>NotE<PERSON>y, <PERSON><PERSON>ptional, IsString, IsUUID } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

export class CreateBrandDto {
  @ApiProperty({ description: 'Name of the brand' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Logo URL', required: false })
  @IsOptional()
  @IsString()
  @IsValidS3Url(FolderType.BRAND_LOGO)
  logoUrl?: string;

  @ApiProperty({ description: 'Include VAT', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  includeVat?: boolean;

  @ApiProperty({ description: 'Assign this merchant user as brand owner (optional)', required: false })
  @IsOptional()
  @IsUUID()
  ownerMerchantUserId?: string;
}
