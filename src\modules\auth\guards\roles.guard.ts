import { UserType } from '@auth/enums/user-type.enum';
import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { IS_OPTIONAL_AUTH_KEY } from '../decorators/optional-auth.decorator';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';
import { ROLES_KEY } from '../decorators/roles.decorator';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // Check if the route is marked as optional auth
    const isOptionalAuth = this.reflector.getAllAndOverride<boolean>(IS_OPTIONAL_AUTH_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If the route is public and not optional auth, allow access without checking roles
    if (isPublic && !isOptionalAuth) {
      return true;
    }

    const requiredRoles = this.reflector.getAllAndMerge<string[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // If no roles are required, allow access
    if (!requiredRoles || requiredRoles.length === 0) {
      throw new ForbiddenException('This API is private.');
    }

    const { user } = context.switchToHttp().getRequest();

    // If optional auth and no user, allow access
    if (isOptionalAuth && !user) {
      return true;
    }

    // If no user is present and not optional auth, deny access
    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (user.userType === UserType.MERCHANT_USER) {
      return true;
    }

    // Check if the user has the required role
    // The format of roles is 'userType:role', e.g., 'ab-admin:superadmin'
    const userTypeRole = `${user.userType}:${user.role}`;
    const userTypeAllRole = `${user.userType}:*`;

    const hasRequiredRole = requiredRoles.some((role) => {
      // Otherwise, check the full userType:role combination
      return userTypeRole === role || userTypeAllRole === role;
    });

    if (!hasRequiredRole) {
      throw new ForbiddenException(`User does not have the permission to access this resource.`);
    }

    return true;
  }
}
