import { Repository } from 'typeorm';

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MerchantUserPermission } from './entities/merchant-user-permission.entity';
import { MerchantUserRole } from './enums/merchant-users-role.enum';

@Injectable()
export class MerchantUserPermissionsService {
  constructor(
    @InjectRepository(MerchantUserPermission)
    private readonly permissionRepository: Repository<MerchantUserPermission>,
  ) {}

  async grantBrandOwner(userId: string, brandId: string): Promise<MerchantUserPermission> {
    return await this.permissionRepository.manager.transaction(async (manager) => {
      await manager.delete(MerchantUserPermission, {
        merchantUserId: userId,
        brandId,
        role: MerchantUserRole.OWNER,
      });
      const permission = manager.create(MerchantUserPermission, {
        merchantUserId: userId,
        brandId,
        role: MerchantUserRole.OWNER,
      });
      await manager.save(permission);
      return permission;
    });
  }
}
