import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { CheckMenuNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-menu-name-exists.dto';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateMenuDto } from './dtos/create-menu.dto';
import { DuplicateMenuDto } from './dtos/duplicate-menu.dto';
import { ListMenuDto } from './dtos/list-menu.dto';
import { UpdateMenuDto } from './dtos/update-menu.dto';
import { Menu } from './entities/menu.entity';
import { MenusService } from './menus.service';

@ApiTags('Menus')
@Controller('menus')
@Roles({ userType: UserType.MERCHANT_USER })
export class MenusController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];
  constructor(private readonly menusService: MenusService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu name exists for restaurant' })
  async checkExists(@Body() dto: CheckMenuNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menusService.checkNameExists(dto.restaurantId, dto.name, dto.excludeId);
    return { exists };
  }

  @Post()
  create(@Body() createMenuDto: CreateMenuDto, @User() user: UserMerchantJwtInfo): Promise<Menu> {
    return this.menusService.create(createMenuDto, user, this.permissionRoles);
  }

  @Get()
  findAll(@Query() listMenuDto: ListMenuDto, @User() user: UserMerchantJwtInfo): Promise<Pagination<Menu>> {
    return this.menusService.findAll(listMenuDto, user, this.permissionRoles);
  }

  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Menu> {
    return this.menusService.activate(id, user, this.permissionRoles);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Menu> {
    return this.menusService.deactivate(id, user, this.permissionRoles);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<Menu> {
    return this.menusService.findOne(id, user, this.permissionRoles);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuDto: UpdateMenuDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Menu> {
    return this.menusService.update(id, updateMenuDto, user, this.permissionRoles);
  }

  @Delete(':id')
  delete(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo) {
    return this.menusService.delete(id, user, this.permissionRoles);
  }

  @Post('duplicate')
  duplicate(@Body() duplicateMenuDto: DuplicateMenuDto, @User() user: UserMerchantJwtInfo) {
    return this.menusService.duplicate(duplicateMenuDto, user, this.permissionRoles);
  }
}
