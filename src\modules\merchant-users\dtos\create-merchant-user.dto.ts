import { IsBoolean, IsEmail, IsNot<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString } from 'class-validator';

import { RemoveLeadingZeroPhone, ToLowerCase } from '@/common/decorators/transforms.decorator';
import { IsPhoneNumber } from '@/common/validators/phone-number.validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMerchantUserDto {
  @ApiProperty({ description: 'Email of the merchant user' })
  @IsNotEmpty()
  @IsEmail()
  @ToLowerCase()
  email: string;

  @ApiProperty({ description: 'First name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Is super admin', required: false })
  @IsOptional()
  @IsBoolean()
  isSuperAdmin?: boolean;

  @ApiProperty({ description: 'Last name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({ description: 'Phone number of the merchant user', required: false })
  @IsOptional()
  @IsPhoneNumber()
  @RemoveLeadingZeroPhone()
  phone?: string;

  @ApiProperty({ description: 'Password of the merchant user' })
  @IsNotEmpty()
  @IsString()
  password: string;
}
