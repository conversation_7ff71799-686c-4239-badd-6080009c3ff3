import { IsBoolean, IsEmail, IsNot<PERSON><PERSON><PERSON>, IsO<PERSON>al, IsString } from 'class-validator';

import { ToLowerCase } from '@/common/decorators/transforms.decorator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMerchantUserDto {
  @ApiProperty({ description: 'Email of the merchant user' })
  @IsNotEmpty()
  @IsEmail()
  @ToLowerCase()
  email: string;

  @ApiProperty({ description: 'First name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Is super admin', required: false })
  @IsOptional()
  @IsBoolean()
  isSuperAdmin?: boolean;

  @ApiProperty({ description: 'Last name of the merchant user' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({ description: 'Password of the merchant user' })
  @IsNotEmpty()
  @IsString()
  password: string;
}
