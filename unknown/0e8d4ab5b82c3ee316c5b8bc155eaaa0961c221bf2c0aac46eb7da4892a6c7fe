import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBankAccountAndReconciliation1753100070263 implements MigrationInterface {
  name = 'AddBankAccountAndReconciliation1753100070263';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "bank_accounts" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "bank_name" character varying NOT NULL, "account_number" character varying NOT NULL, "account_holder" character varying NOT NULL, "bank_branch" character varying NOT NULL, "branch_address" character varying NOT NULL, "restaurant_id" uuid, "brand_id" uuid, CONSTRAINT "PK_c872de764f2038224a013ff25ed" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b66035c96912de4941c23f0ba4" ON "bank_accounts" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6179314a33db1767f5d76a5041" ON "bank_accounts" ("brand_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE TYPE "public"."reconciliations_settlement_plan_enum" AS ENUM('3', '30')`);
    await queryRunner.query(`CREATE TYPE "public"."reconciliations_next_settlement_plan_enum" AS ENUM('3', '30')`);
    await queryRunner.query(
      `CREATE TABLE "reconciliations" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "restaurant_id" uuid NOT NULL, "settlement_plan" "public"."reconciliations_settlement_plan_enum" NOT NULL, "bank_id" uuid NOT NULL, "start_time" TIMESTAMP WITH TIME ZONE NOT NULL, "end_time" TIMESTAMP WITH TIME ZONE NOT NULL, "next_settlement_plan" "public"."reconciliations_next_settlement_plan_enum" NOT NULL, "next_bank_id" uuid NOT NULL, CONSTRAINT "REL_af1d523d1e979af8f3d468a443" UNIQUE ("restaurant_id"), CONSTRAINT "PK_110f3839ca29e2fd8ff4aaec7b8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_4dc903ccd8a4d39986d64e43fb" ON "reconciliations" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_0543c28efa565975fd17a8bc7b" ON "reconciliations" ("settlement_plan") `);
    await queryRunner.query(`CREATE INDEX "IDX_b1d1ee96ffadef2956fd1d94ae" ON "reconciliations" ("bank_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_a0fef43e682b6aeb98b98ea816" ON "reconciliations" ("start_time") `);
    await queryRunner.query(`CREATE INDEX "IDX_c0dbeb41e692716e3797485bbf" ON "reconciliations" ("end_time") `);
    await queryRunner.query(
      `ALTER TABLE "bank_accounts" ADD CONSTRAINT "FK_c4d086176480f0130868e819598" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "bank_accounts" ADD CONSTRAINT "FK_e4e8519461da04ca5cf47c7f3f0" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_af1d523d1e979af8f3d468a4433" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_b1d1ee96ffadef2956fd1d94ae4" FOREIGN KEY ("bank_id") REFERENCES "bank_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f" FOREIGN KEY ("next_bank_id") REFERENCES "bank_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f"`);
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_b1d1ee96ffadef2956fd1d94ae4"`);
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_af1d523d1e979af8f3d468a4433"`);
    await queryRunner.query(`ALTER TABLE "bank_accounts" DROP CONSTRAINT "FK_e4e8519461da04ca5cf47c7f3f0"`);
    await queryRunner.query(`ALTER TABLE "bank_accounts" DROP CONSTRAINT "FK_c4d086176480f0130868e819598"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c0dbeb41e692716e3797485bbf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a0fef43e682b6aeb98b98ea816"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b1d1ee96ffadef2956fd1d94ae"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0543c28efa565975fd17a8bc7b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4dc903ccd8a4d39986d64e43fb"`);
    await queryRunner.query(`DROP TABLE "reconciliations"`);
    await queryRunner.query(`DROP TYPE "public"."reconciliations_next_settlement_plan_enum"`);
    await queryRunner.query(`DROP TYPE "public"."reconciliations_settlement_plan_enum"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6179314a33db1767f5d76a5041"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b66035c96912de4941c23f0ba4"`);
    await queryRunner.query(`DROP TABLE "bank_accounts"`);
  }
}
