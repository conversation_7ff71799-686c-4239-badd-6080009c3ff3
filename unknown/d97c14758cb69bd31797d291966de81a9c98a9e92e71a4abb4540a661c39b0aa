import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateReconciliation1753971268100 implements MigrationInterface {
  name = 'UpdateReconciliation1753971268100';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "settlement_plan" SET DEFAULT '30'`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "start_time" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "end_time" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "next_settlement_plan" DROP NOT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "next_settlement_plan" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "end_time" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "start_time" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "settlement_plan" DROP DEFAULT`);
  }
}
