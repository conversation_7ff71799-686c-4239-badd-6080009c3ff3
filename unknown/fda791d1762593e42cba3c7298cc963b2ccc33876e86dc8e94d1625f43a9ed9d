import { Column, Entity, Index, JoinColumn, ManyToOne, OneToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { BankAccount } from '@/modules/bank-accounts/entities/bank-account.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { SettlementPlan } from '../enums/settlement-plan.enum';

@Entity('reconciliations')
export class Reconciliation extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'settlement_plan', type: 'enum', enum: SettlementPlan, default: SettlementPlan.THIRTY_DAYS })
  settlementPlan: SettlementPlan;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'bank_id', type: 'uuid', nullable: true })
  bankId: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'start_time', type: 'timestamptz', nullable: true })
  startTime: Date | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'end_time', type: 'timestamptz', nullable: true })
  endTime: Date | null;

  @Column({ name: 'next_settlement_plan', type: 'enum', enum: SettlementPlan, nullable: true })
  nextSettlementPlan: SettlementPlan | null;

  @OneToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @ManyToOne(() => BankAccount)
  @JoinColumn({ name: 'bank_id' })
  bankAccount: WrapperType<BankAccount>;
}
