import { BankAccountsModule } from '@/modules/bank-accounts/bank-accounts.module';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Reconciliation } from './entities/reconciliation.entity';
import { ReconciliationsController } from './reconciliations.controller';
import { ReconciliationsService } from './reconciliations.service';

@Module({
  imports: [TypeOrmModule.forFeature([Reconciliation]), forwardRef(() => BankAccountsModule)],
  controllers: [ReconciliationsController],
  providers: [ReconciliationsService],
  exports: [ReconciliationsService],
})
export class ReconciliationsModule {}
