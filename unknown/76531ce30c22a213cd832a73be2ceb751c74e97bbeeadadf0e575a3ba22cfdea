import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne, PrimaryColumn } from 'typeorm';

import { MenuSection } from '@/modules/menu-sections/entities/menu-section.entity';

import { Menu } from './menu.entity';

@Entity('mapping_menus_menu_sections')
export class MappingMenuMenuSection {
  @Index()
  @PrimaryColumn({ name: 'menu_id', type: 'uuid' })
  menuId: string;

  @Index()
  @PrimaryColumn({ name: 'menu_section_id', type: 'uuid' })
  menuSectionId: string;

  @Column({ name: 'position', type: 'int', default: 0 })
  position: number;

  @ManyToOne(() => Menu, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_id' })
  menu: WrapperType<Menu>;

  @ManyToOne(() => MenuSection, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_section_id' })
  menuSection: WrapperType<MenuSection>;
}
