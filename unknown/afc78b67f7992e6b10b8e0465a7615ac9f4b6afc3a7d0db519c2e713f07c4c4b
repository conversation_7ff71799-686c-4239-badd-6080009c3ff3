import { Column, Entity, Generated, Index, JoinColumn, JoinTable, ManyToMany, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

@Entity('ingredients')
@Index(['code', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['internalName', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['publishedName', 'restaurantId'], { unique: true, where: 'deleted_at IS NULL' })
export class Ingredient extends BaseEntity {
  @Generated('uuid')
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'code', type: 'uuid' })
  code: string;

  @Column({ name: 'internal_name', type: 'varchar' })
  internalName: string;

  @Column({ name: 'published_name', type: 'varchar' })
  publishedName: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @ManyToMany(() => MenuItem, (menuItem) => menuItem.ingredients)
  @JoinTable({
    name: 'mapping_menu_items_ingredients',
    joinColumn: {
      name: 'ingredient_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'menu_item_id',
      referencedColumnName: 'id',
    },
  })
  menuItems: WrapperType<MenuItem>[];
}
