import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MerchantStaff } from '@/modules/merchant-staff/entities/merchant-staff.entity';
import { FcmPlatform } from '@/modules/users/enums/fcm-platform.enum';

@Entity('staff_fcm_tokens')
@Index(['merchantStaffId', 'deviceId'], { unique: true, where: 'deleted_at IS NULL' })
@Index(['merchantStaffId'], { where: 'deleted_at IS NULL' })
@Index(['token'], { where: 'deleted_at IS NULL' })
export class StaffFcmToken extends BaseEntity {
  @Column({ name: 'merchant_staff_id', type: 'uuid' })
  merchantStaffId: string;

  @Column()
  token: string;

  @Column({ name: 'device_id', type: 'varchar' })
  deviceId: string;

  @Column({ type: 'varchar' })
  platform: FcmPlatform;

  @ManyToOne(() => MerchantStaff)
  @JoinColumn({ name: 'merchant_staff_id' })
  merchantStaff: WrapperType<MerchantStaff>;
}
