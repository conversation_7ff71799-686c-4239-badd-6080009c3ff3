import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBrandLogoAndUniqueName1753109624630 implements MigrationInterface {
  name = 'AddBrandLogoAndUniqueName1753109624630';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "brands" ADD "logo_url" character varying`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_be90a9401ee4507d8dbff22212" ON "merchant_accounts" ("name") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_eb9f7db6760bbc4920e2fc5ef9" ON "brands" ("name") WHERE deleted_at IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_eb9f7db6760bbc4920e2fc5ef9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_be90a9401ee4507d8dbff22212"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "logo_url"`);
  }
}
