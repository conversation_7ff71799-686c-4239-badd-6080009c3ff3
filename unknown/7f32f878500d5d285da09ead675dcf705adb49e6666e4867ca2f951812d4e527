import { MigrationInterface, QueryRunner } from 'typeorm';

export class IndexOptimization1753708328024 implements MigrationInterface {
  name = 'IndexOptimization1753708328024';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_9acd2767cf71e4107b4dfd3fcb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_869ca568c4ec52322f1681b1a3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2aac7de7c15294c7f90a51d065"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b32ebc7389b31249cec583b622"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_09df5343f9d0f4e24c320f4e47"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6be527e7fe4a095222ee917cdf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b132a6f2a24dabfce5bd3e8d5d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_359b53d73273490ee0f0afda47"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_51cda18866e732ce9138ee3e01"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_651a142582b49f73fcaed66772"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8b44b2384fd44f07294e83b88b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_291910d4875ae08b2202f08eff"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3f3748c03ffce9383bc4760e1f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2988f46a1a98b221a5ffcc641b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4c32753d45094d0f2f7bf05bef"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ba96bbd861b8b0b4c2d2280a45"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b9ec5028095e6f37875372bccd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0e7c37bcce8087e08311f7ffa5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ca6f3ba1951e27c19e27c229f8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_42b75f4146dd6c5facd0369007"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8d1ee4780bf64ae94cbf3e5370"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_806e6b8c727b7b60558b966df4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a4b2bf594216fc1e0a570dd02a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_216807fa8aeb465c9715e2d1cd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3bbc3e40de7b87601f907cf196"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ec73ac3da075000d0b7cbfafe4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bcd4a935c967cc9c20e770d1e6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6b878682f9c53cbfe804ae288c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9b9626d392ff967d6715178a3e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0543c28efa565975fd17a8bc7b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b1d1ee96ffadef2956fd1d94ae"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a0fef43e682b6aeb98b98ea816"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c0dbeb41e692716e3797485bbf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3bd1ca14eecd347cd0ae6ae169"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f2d957e44b59ca1e7f45613c08"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6de2961809c2a7f90f20d45d7e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_75eb8d9c79834a4a34b7fa5eb4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a98ed620228a82ee3f59d57de4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_93448093be47625ee44fef5240"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5af4db15acb151b8ebc046a6b7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f46069b19d64ec43ff6b24d390"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4ff0af8a480bd8ad020ea03123"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_54b296fa045ada3fc08af06bb8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a6ea8735a2b5ce2f792ff7196"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d544d2bc07b3107ac0dde71d35"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b39d7c4712fd9a0739e034919f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_61f1d453ca3c6f56322e44104a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a954cc97827e9ec3a22a923169"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7f9e5689ad3d7f45172fc156fb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2d0e434f89f68688e0845d6639"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a02030a25506b8e2b1d21ad0a2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f352983724f663a30afcf73c88"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c134d148ae4c3ce4d094c68db3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_892cef7cd3962597f0a1c00679"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7adfabf9e92e69bb0e1f83c439"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e9b23b2023b27cbd08de3b706a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7c67df01f06b0bba0fc0c94560"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0379c1cb8cd158363b657fd849"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8ceccd31f4e29422b4a07bc527"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_88781f54067d681e71c89b159c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f2651c1fc5cda6c248cf9a9600"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d57c8458bc9f4d1b1ce0279e08"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b02e9a1d9c5bb702c399c64532"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a1f816622fc3b59d1f2e752134"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ab8ab3f2d51d7fa980975b44cb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1203311ef704aec0de93518b1f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_43f343e9f7ae36dcad50b06431"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d0218619c7cb8b730fec7b8345"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d85d9cf116ef2899c1527e0161"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_737f31011da2b353b59c964a6d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c7290a51d662471959fd09a5b0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6371c678ba78d1240296ed4bd1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d4b322024da0b678ae8ab161a3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bef84e6cb0e1aaba85edc36896"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6b4075801b9e18b2dc315289f2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_28a1c8c94031b5f8d01c391eb8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7833a02c6d70444b115d8d82a9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c0cd807cd918bd558cc62e6b23"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4c3a39442f6a176783250ecd1e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0285b10a8f28dfca7c151dc560"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7c843fb0876f77a674a6d1b616"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_29838d741c11c305b7d9a94433"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_09ceef583125688ec741ddcd50"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e8232648e5a62bb1fcee28cb1a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3d623662d4ee1219b23cf61e64"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ae763c9552786328a3d8002c93"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5588b6cea298cedec7063c0d33"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2b27bb087dddab938b07f09ac3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_56a790ed62e868f2e0c5f81115"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_709afc71fa11ad33309070de99"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_25c23b033c23996ea4b09449c5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bdfd8165e694fe9d3ce4140c87"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1d43fff33753a7c91679fc2eb2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_38b72c260431ecb3e528214474"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ad50548131c585e38d765bde16"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8b6352f8592c91c512a89ce274"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d3e515f831ca3520b96c36d7e9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_764a2ab4e49ad36d1f9eca9cc9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a4ea236d6e82865f48820980f9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b65e8d6b262ae8a2014e86c823"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_e6445ccf048b3e0c96d0b9f1f6" ON "user_fcm_tokens" ("token") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_514577843302e78a807004654f" ON "user_fcm_tokens" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_308c50af5d57940a72e7c7960e" ON "users" ("phone") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_251cf3994745de94d808c5a17c" ON "users" ("phoneCountryCode") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3196d110b2177fb1c8d06e977b" ON "users" ("banned") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_29526ffd72925055b24f3304dc" ON "users" ("language") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_95a88da3198d7f9e7f756a5027" ON "user_change_logs" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2ae18b380732c4f5cf1e89d8b2" ON "user_change_logs" ("user_id", "change_type") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a3da54c4ede1902c343681ced3" ON "merchant_users" ("role") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fa25e63b99e71e748192f33ddb" ON "merchant_users" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e95432b8763f4beba5842ccab0" ON "merchant_users" ("banned") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_eb77981778960cdbe11559d16c" ON "merchant_accounts" ("owner_merchant_user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e0093602b8bcd9a95099efab90" ON "merchant_accounts" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_524005667ca5a052dcb55a5a82" ON "brands" ("merchant_account_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e54d25a6eb6d1ebc0d433a2290" ON "brands" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_965430bb81b08935f5c4def2d8" ON "geofencing" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b8557b365d83b4ab1a360160e2" ON "geofencing" ("shipping_fee") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_75f65c1c79968139352619a02b" ON "ingredients" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_eeaec8d58d6fb2adf2ef7ebd59" ON "ingredients" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ba1c60c5479cb4e99224d9c51b" ON "mapping_menu_items_menu_item_option_groups" ("menu_item_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a3e12df0bfd4c9c19c110058c3" ON "mapping_menu_items_menu_item_option_groups" ("menu_item_option_group_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_d3668e468b23af780554818230" ON "menu_item_option_groups" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a9f53a19e0bbb8579ffafc1cc4" ON "menu_item_option_groups" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8aef769c00278d1885c9745394" ON "mapping_menu_item_option_groups_menu_item_options" ("menu_item_option_group_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5fee1f9aa4a79c2e513c99615b" ON "mapping_menu_item_option_groups_menu_item_options" ("menu_item_option_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7d8f3e64fb636921da1485fdc9" ON "mapping_menu_item_option_groups_menu_item_options" ("menu_item_option_group_id", "menu_item_option_id", "price") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_ec3aac5398c021cff30b6bd647" ON "menu_items" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e72879ed420b9bcc1ed911a7cc" ON "menu_items" ("published_name") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c28b997503bdd2ddc8cf4db27f" ON "menu_items" ("type") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_82559c0e47bf43b2ade040c6d8" ON "menu_items" ("is_alcohol") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7f3dd4010ca1fc8c9d14da609a" ON "menu_items" ("total_orders_sold") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e83f21d1df6379fb2b4316ba6e" ON "menu_items" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1260a51df43d946962e9d69ae6" ON "menu_items" ("schedule_active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4d008c94da63346ccddd4a77dd" ON "menu_items" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2b16234bfb13137f0fe6a22482" ON "mapping_menu_sections_menu_items" ("menu_section_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6f1feaa66899b1dab4d1f54436" ON "mapping_menu_sections_menu_items" ("menu_item_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c10ad4035ecf6c0f82342fb07a" ON "mapping_menu_sections_menu_items" ("menu_section_id", "menu_item_id", "price") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_42bfbd5bdf3d527b47756cf898" ON "menu_section_available_schedules" ("menu_section_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6e7c9b35bcfc9a24151263aa80" ON "menu_section_available_schedules" ("day") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b36c799b0ec8fa6c1aafa8b596" ON "menu_section_available_schedules" ("start") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_631ed30f788ffacdaeb39361e2" ON "menu_section_available_schedules" ("end") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cf8add8e13762943fa2f8b9831" ON "menu_section_available_schedules" ("is_all_day") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_dc3ed459c665ef50dbe96cf972" ON "menu_sections" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_24fdfcc0c4e27251ea9c9b4425" ON "menu_sections" ("schedule_active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_337f18827a100da5916cf22974" ON "menu_sections" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_33876e3eea420f12f6bf9fdb87" ON "menu_sections" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7b48e6f25f9f0fc09eab6ff75c" ON "mapping_menus_menu_sections" ("menu_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f9f651425f175f4a9a4e2bab41" ON "mapping_menus_menu_sections" ("menu_section_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_7d4cd7bf37509d7930d099c341" ON "menus" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_55e6c6175d9f7f6642dfa99b80" ON "menus" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fa185adf0571941fcaae21f7d2" ON "menus" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9f5a84168b339d775b5b6f43ac" ON "reconciliations" ("settlement_plan") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7eb75dae2daab847135d7fdcbb" ON "reconciliations" ("bank_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f35e6430ba8326190006f8892a" ON "reconciliations" ("start_time") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6d1e9b5d6913c8ef7ff98b0b61" ON "reconciliations" ("end_time") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c2333fd79b5f8e473155b5a15a" ON "restaurant_available_schedules" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_853ec0706736df10b83203cdd1" ON "restaurant_available_schedules" ("day") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3639dc638b5a83a98ce4701aec" ON "restaurant_available_schedules" ("start") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5c8242a92baedca96369414b88" ON "restaurant_available_schedules" ("end") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7df8b64aaada35e98f86d2f8df" ON "restaurant_available_schedules" ("is_all_day") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bfd674c23aba2f1d7ed8383718" ON "restaurants" ("price_range") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c0d8c09f8505e48a387265655" ON "restaurants" ("star_rated") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_16eeefd4a4d276a6ee882f830d" ON "restaurants" ("total_reviews") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8f6707d9e43a55c18eeabca1f4" ON "restaurants" ("total_orders_sold") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8f1d6bba9566f5a5ea27862493" ON "restaurants" ("brand_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fc6c736f8473ae5d4533afc585" ON "restaurants" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fb13af5afa4a7ff7d323b6bea9" ON "restaurants" ("schedule_active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f395c55ef28e01c2315cfec138" ON "restaurants" ("latitude") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_06c856ed636c480913e5fa9e3d" ON "restaurants" ("longitude") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_aea98cdc13b22c19776565b65f" ON "restaurants" ("brand_id", "active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4ef780cd4ce8e286730b812888" ON "user_favourite_restaurants" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f43c7f07681863c166783a72a8" ON "user_favourite_restaurants" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1e4ec6cda7989feebd954483d0" ON "user-addresses" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0b14a3c88557141c7e8012bfc1" ON "user-addresses" ("latitude", "longitude") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a799c756cc109e13ed9eedf641" ON "user-addresses" ("user_id", "is_temporary") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ff38b4ad5027970fc1e1d6286d" ON "chat_conversations" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7ebdcece51cc05568878f94f4a" ON "chat_conversations" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_efa5ab5deec280254b68e0e088" ON "staff_fcm_tokens" ("token") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3cac4dcd6e19664dd5d0b15966" ON "staff_fcm_tokens" ("merchant_staff_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9dbab232fec22937372d724fa5" ON "merchant_staff" ("role") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_241bcad195b3ffb82866685cf6" ON "merchant_staff" ("banned") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_46a791e729255a83a554402bb0" ON "merchant_staff" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c1cd1eb17af2aa166f1b997c46" ON "order_item_options" ("order_item_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_38186a6957213f581e833faf61" ON "order_items" ("order_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fba5f66e96d49552b256adfd87" ON "orders" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_77898b6f0a94dfd9090aa23991" ON "orders" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_941af0da90c0b1698a126c5f99" ON "orders" ("order_sequence_number") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_837d1d72879ee78e8bb6f99c22" ON "orders" ("status") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_590d98e2d168c3885b3835cc81" ON "orders" ("payment_method") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_32ec157cd56ad95398370bebfb" ON "orders" ("payment_card_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9b6ad165c339e8ef25a4bbbceb" ON "orders" ("payment_status") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fe017c6d543f0f3fbbef85712c" ON "orders" ("restaurant_id", "order_sequence_number") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2d20665bf62a4647c02fe8df91" ON "orders" ("restaurant_id", "status", "updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2da24723ad0fece048b5bd9ad3" ON "orders" ("user_id", "status", "updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_51430ab654d31f5104bbcf1ead" ON "orders" ("user_id", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_93b9544400df9929a65a29b5f4" ON "restaurant_review_replies" ("restaurant_review_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ae1dcecb154db967edc7284fa8" ON "restaurant_review_replies" ("merchant_staff_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b03ce94449127d78973b0fc797" ON "restaurant_review_replies" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1f6bf962b0cdd8a17d82c97cd3" ON "restaurant_review_replies" ("restaurant_review_id", "created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d329d27b84d587547fca62b7d8" ON "restaurant_reviews" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_18c67b46ac9760238c09a25f9d" ON "restaurant_reviews" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5ab6824523154dc0767af0ae1d" ON "restaurant_reviews" ("order_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5ad8848a3b9fc01165e1285f07" ON "restaurant_reviews" ("rating") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5f2c3da2d2b1cf2616ccef2896" ON "restaurant_reviews" ("user_id", "restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5adf07f7cb7ddbe15027e2b7a8" ON "restaurant_reviews" ("restaurant_id", "created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cff991e11a4ba656fb30f58e50" ON "mapping_restaurant_review_tags" ("restaurant_review_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a8209057d1a2c75464d22765a" ON "mapping_restaurant_review_tags" ("review_tag_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fbf0922bda602edcc6f257f60b" ON "review_tags" ("active") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2a84d7dfcc0a8ef8a75ce69ece" ON "user_payment_cards" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8f93f0da57b1da649251b43402" ON "onepay_order_requests" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c462a3a8d52bb853d47471c0ad" ON "onepay_order_requests" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_fc812c53a8abdf24d2b2fde995" ON "onepay_order_requests" ("merch_txn_ref") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_edfda2fdf24db371b7747adbd3" ON "onepay_order_requests" ("is_refunded") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_986430cdd122e654c6b2f3b5d7" ON "onepay_order_requests" ("dr_exists") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_07e3345b24ca98459aff32d39d" ON "onepay_order_requests" ("txn_response_code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d90c1c417ee37988eeebc0d68e" ON "onepay_order_requests" ("updated_at", "txn_response_code", "dr_exists") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9690073d7ffbb0af6d4a24463e" ON "onepay_create_token_requests" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_676caead46ddfbf91cda58ad07" ON "onepay_create_token_requests" ("merch_txn_ref") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6b47c9d20413993090b8ced049" ON "onepay_create_token_requests" ("dr_exists") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_269f4e944ea4762625a37aba4c" ON "onepay_create_token_requests" ("txn_response_code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2f1c31178c78cf2cfa2bf3d259" ON "onepay_create_token_requests" ("txn_response_code", "is_refunded", "retry_count") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3ab4ce923d17369899499270d7" ON "onepay_create_token_requests" ("updated_at", "txn_response_code", "dr_exists") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_11c584310108ac590a59e67abc" ON "chat_messages" ("conversation_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8000bdb9c545bafeba055b06ca" ON "chat_messages" ("sender_type") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a2eef1e99d3cda11e25992736d" ON "chat_messages" ("user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8d765a7d70ee48d316a146afa3" ON "chat_messages" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_783111236dd279853d322b7f89" ON "chat_messages" ("status") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5e52e4bb890df0b7fe997876a0" ON "chat_messages" ("restaurant_id", "status") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ef816e23fa6830acb33cb52a8c" ON "chat_messages" ("user_id", "status") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2a3fdfb537ba040eac55eccc9a" ON "chat_messages" ("conversation_id", "created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_584a1024bc1d09813b409f61c0" ON "cart_item_options" ("cart_item_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_06bd82ebc65673e5cba7f93f6d" ON "cart_item_options" ("menu_item_option_group_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5d330adf3dca7bc2c32856bba3" ON "cart_item_options" ("menu_item_option_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9c6f621bce4a4b1808c3b0a501" ON "cart_items" ("cart_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c89ddee3ebcfa731cff875acb6" ON "cart_items" ("menu_item_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ccd6a2e0b59907b22b22b5b4ea" ON "cart_items" ("menu_section_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_43bd4f9575d1e540d0185ab054" ON "cart_items" ("cart_id", "menu_item_id", "menu_section_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dd9661ce83cb82fe07cde51cc9" ON "carts" ("user_id") WHERE deleted_at IS NULL AND completed_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_369f8897457e5bdcb84dfb37ec" ON "carts" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5d111e982d3ae9b0378507fbaa" ON "carts" ("completed_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_061a0e05ca4228d5a0882df1f3" ON "carts" ("restaurant_id", "user_id", "completed_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a9848240bdb2b8302d5e3ff8c5" ON "admin" ("role") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_951fea1f5c508ff38a3334b7ff" ON "admin" ("banned") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7239cd04461f969d06565398e3" ON "app_versions" ("platform") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e6b2fe350ae59dc2e08e2ae2ab" ON "app_versions" ("is_active") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d290aa71f331edd46c340c8aac" ON "app_versions" ("released_at") WHERE deleted_at IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_d290aa71f331edd46c340c8aac"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6b2fe350ae59dc2e08e2ae2ab"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7239cd04461f969d06565398e3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_951fea1f5c508ff38a3334b7ff"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a9848240bdb2b8302d5e3ff8c5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_061a0e05ca4228d5a0882df1f3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5d111e982d3ae9b0378507fbaa"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_369f8897457e5bdcb84dfb37ec"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dd9661ce83cb82fe07cde51cc9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_43bd4f9575d1e540d0185ab054"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ccd6a2e0b59907b22b22b5b4ea"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c89ddee3ebcfa731cff875acb6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9c6f621bce4a4b1808c3b0a501"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5d330adf3dca7bc2c32856bba3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_06bd82ebc65673e5cba7f93f6d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_584a1024bc1d09813b409f61c0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2a3fdfb537ba040eac55eccc9a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ef816e23fa6830acb33cb52a8c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5e52e4bb890df0b7fe997876a0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_783111236dd279853d322b7f89"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8d765a7d70ee48d316a146afa3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a2eef1e99d3cda11e25992736d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8000bdb9c545bafeba055b06ca"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_11c584310108ac590a59e67abc"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3ab4ce923d17369899499270d7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2f1c31178c78cf2cfa2bf3d259"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_269f4e944ea4762625a37aba4c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6b47c9d20413993090b8ced049"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_676caead46ddfbf91cda58ad07"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9690073d7ffbb0af6d4a24463e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d90c1c417ee37988eeebc0d68e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_07e3345b24ca98459aff32d39d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_986430cdd122e654c6b2f3b5d7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_edfda2fdf24db371b7747adbd3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fc812c53a8abdf24d2b2fde995"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c462a3a8d52bb853d47471c0ad"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8f93f0da57b1da649251b43402"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2a84d7dfcc0a8ef8a75ce69ece"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fbf0922bda602edcc6f257f60b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a8209057d1a2c75464d22765a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cff991e11a4ba656fb30f58e50"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5adf07f7cb7ddbe15027e2b7a8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5f2c3da2d2b1cf2616ccef2896"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5ad8848a3b9fc01165e1285f07"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5ab6824523154dc0767af0ae1d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_18c67b46ac9760238c09a25f9d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d329d27b84d587547fca62b7d8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1f6bf962b0cdd8a17d82c97cd3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b03ce94449127d78973b0fc797"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ae1dcecb154db967edc7284fa8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_93b9544400df9929a65a29b5f4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_51430ab654d31f5104bbcf1ead"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2da24723ad0fece048b5bd9ad3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2d20665bf62a4647c02fe8df91"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fe017c6d543f0f3fbbef85712c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9b6ad165c339e8ef25a4bbbceb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_32ec157cd56ad95398370bebfb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_590d98e2d168c3885b3835cc81"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_837d1d72879ee78e8bb6f99c22"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_941af0da90c0b1698a126c5f99"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_77898b6f0a94dfd9090aa23991"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fba5f66e96d49552b256adfd87"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_38186a6957213f581e833faf61"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c1cd1eb17af2aa166f1b997c46"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_46a791e729255a83a554402bb0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_241bcad195b3ffb82866685cf6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9dbab232fec22937372d724fa5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3cac4dcd6e19664dd5d0b15966"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_efa5ab5deec280254b68e0e088"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7ebdcece51cc05568878f94f4a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ff38b4ad5027970fc1e1d6286d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a799c756cc109e13ed9eedf641"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0b14a3c88557141c7e8012bfc1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1e4ec6cda7989feebd954483d0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f43c7f07681863c166783a72a8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4ef780cd4ce8e286730b812888"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_aea98cdc13b22c19776565b65f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_06c856ed636c480913e5fa9e3d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f395c55ef28e01c2315cfec138"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fb13af5afa4a7ff7d323b6bea9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fc6c736f8473ae5d4533afc585"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8f1d6bba9566f5a5ea27862493"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8f6707d9e43a55c18eeabca1f4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_16eeefd4a4d276a6ee882f830d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7c0d8c09f8505e48a387265655"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bfd674c23aba2f1d7ed8383718"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7df8b64aaada35e98f86d2f8df"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5c8242a92baedca96369414b88"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3639dc638b5a83a98ce4701aec"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_853ec0706736df10b83203cdd1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c2333fd79b5f8e473155b5a15a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6d1e9b5d6913c8ef7ff98b0b61"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f35e6430ba8326190006f8892a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7eb75dae2daab847135d7fdcbb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9f5a84168b339d775b5b6f43ac"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fa185adf0571941fcaae21f7d2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_55e6c6175d9f7f6642dfa99b80"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7d4cd7bf37509d7930d099c341"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f9f651425f175f4a9a4e2bab41"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7b48e6f25f9f0fc09eab6ff75c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_33876e3eea420f12f6bf9fdb87"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_337f18827a100da5916cf22974"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_24fdfcc0c4e27251ea9c9b4425"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dc3ed459c665ef50dbe96cf972"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cf8add8e13762943fa2f8b9831"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_631ed30f788ffacdaeb39361e2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b36c799b0ec8fa6c1aafa8b596"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6e7c9b35bcfc9a24151263aa80"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_42bfbd5bdf3d527b47756cf898"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c10ad4035ecf6c0f82342fb07a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6f1feaa66899b1dab4d1f54436"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2b16234bfb13137f0fe6a22482"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4d008c94da63346ccddd4a77dd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1260a51df43d946962e9d69ae6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e83f21d1df6379fb2b4316ba6e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7f3dd4010ca1fc8c9d14da609a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_82559c0e47bf43b2ade040c6d8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c28b997503bdd2ddc8cf4db27f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e72879ed420b9bcc1ed911a7cc"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ec3aac5398c021cff30b6bd647"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7d8f3e64fb636921da1485fdc9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5fee1f9aa4a79c2e513c99615b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8aef769c00278d1885c9745394"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a9f53a19e0bbb8579ffafc1cc4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d3668e468b23af780554818230"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a3e12df0bfd4c9c19c110058c3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ba1c60c5479cb4e99224d9c51b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_eeaec8d58d6fb2adf2ef7ebd59"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_75f65c1c79968139352619a02b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b8557b365d83b4ab1a360160e2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_965430bb81b08935f5c4def2d8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e54d25a6eb6d1ebc0d433a2290"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_524005667ca5a052dcb55a5a82"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e0093602b8bcd9a95099efab90"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_eb77981778960cdbe11559d16c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e95432b8763f4beba5842ccab0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fa25e63b99e71e748192f33ddb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a3da54c4ede1902c343681ced3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2ae18b380732c4f5cf1e89d8b2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_95a88da3198d7f9e7f756a5027"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_29526ffd72925055b24f3304dc"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3196d110b2177fb1c8d06e977b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_251cf3994745de94d808c5a17c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_308c50af5d57940a72e7c7960e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_514577843302e78a807004654f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6445ccf048b3e0c96d0b9f1f6"`);
    await queryRunner.query(`CREATE INDEX "IDX_b65e8d6b262ae8a2014e86c823" ON "app_versions" ("released_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_a4ea236d6e82865f48820980f9" ON "app_versions" ("is_active") `);
    await queryRunner.query(`CREATE INDEX "IDX_764a2ab4e49ad36d1f9eca9cc9" ON "app_versions" ("platform") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d3e515f831ca3520b96c36d7e9" ON "carts" ("deleted_at", "user_id", "restaurant_id", "completed_at") WHERE ((deleted_at IS NULL) AND (completed_at IS NULL))`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_8b6352f8592c91c512a89ce274" ON "carts" ("completed_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_ad50548131c585e38d765bde16" ON "carts" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_38b72c260431ecb3e528214474" ON "cart_items" ("cart_id", "menu_item_id", "menu_section_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_1d43fff33753a7c91679fc2eb2" ON "cart_item_options" ("cart_item_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_bdfd8165e694fe9d3ce4140c87" ON "chat_messages" ("created_at", "conversation_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_25c23b033c23996ea4b09449c5" ON "chat_messages" ("user_id", "status") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_709afc71fa11ad33309070de99" ON "chat_messages" ("restaurant_id", "status") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_56a790ed62e868f2e0c5f81115" ON "chat_messages" ("status") `);
    await queryRunner.query(`CREATE INDEX "IDX_2b27bb087dddab938b07f09ac3" ON "chat_messages" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_5588b6cea298cedec7063c0d33" ON "chat_messages" ("user_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_ae763c9552786328a3d8002c93" ON "chat_messages" ("sender_type") `);
    await queryRunner.query(`CREATE INDEX "IDX_3d623662d4ee1219b23cf61e64" ON "chat_messages" ("conversation_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_e8232648e5a62bb1fcee28cb1a" ON "onepay_create_token_requests" ("updated_at", "dr_exists", "txn_response_code") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_09ceef583125688ec741ddcd50" ON "onepay_create_token_requests" ("updated_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_29838d741c11c305b7d9a94433" ON "onepay_create_token_requests" ("is_refunded", "retry_count", "txn_response_code") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c843fb0876f77a674a6d1b616" ON "onepay_create_token_requests" ("txn_response_code") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0285b10a8f28dfca7c151dc560" ON "onepay_create_token_requests" ("dr_exists") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_4c3a39442f6a176783250ecd1e" ON "onepay_create_token_requests" ("merch_txn_ref") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c0cd807cd918bd558cc62e6b23" ON "onepay_create_token_requests" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7833a02c6d70444b115d8d82a9" ON "onepay_order_requests" ("updated_at", "dr_exists", "txn_response_code") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_28a1c8c94031b5f8d01c391eb8" ON "onepay_order_requests" ("updated_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_6b4075801b9e18b2dc315289f2" ON "onepay_order_requests" ("txn_response_code") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_bef84e6cb0e1aaba85edc36896" ON "onepay_order_requests" ("dr_exists") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d4b322024da0b678ae8ab161a3" ON "onepay_order_requests" ("is_refunded") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_6371c678ba78d1240296ed4bd1" ON "onepay_order_requests" ("merch_txn_ref") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c7290a51d662471959fd09a5b0" ON "onepay_order_requests" ("restaurant_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_737f31011da2b353b59c964a6d" ON "onepay_order_requests" ("user_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_d85d9cf116ef2899c1527e0161" ON "review_tags" ("active") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d0218619c7cb8b730fec7b8345" ON "restaurant_reviews" ("created_at", "restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_43f343e9f7ae36dcad50b06431" ON "restaurant_reviews" ("user_id", "restaurant_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_1203311ef704aec0de93518b1f" ON "restaurant_reviews" ("rating") `);
    await queryRunner.query(`CREATE INDEX "IDX_ab8ab3f2d51d7fa980975b44cb" ON "restaurant_reviews" ("order_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_a1f816622fc3b59d1f2e752134" ON "restaurant_reviews" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_b02e9a1d9c5bb702c399c64532" ON "restaurant_reviews" ("user_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d57c8458bc9f4d1b1ce0279e08" ON "restaurant_review_replies" ("created_at", "restaurant_review_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f2651c1fc5cda6c248cf9a9600" ON "restaurant_review_replies" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_88781f54067d681e71c89b159c" ON "restaurant_review_replies" ("merchant_staff_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8ceccd31f4e29422b4a07bc527" ON "restaurant_review_replies" ("restaurant_review_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_0379c1cb8cd158363b657fd849" ON "orders" ("user_id", "restaurant_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_7c67df01f06b0bba0fc0c94560" ON "orders" ("updated_at", "user_id", "status") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e9b23b2023b27cbd08de3b706a" ON "orders" ("updated_at", "restaurant_id", "status") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7adfabf9e92e69bb0e1f83c439" ON "orders" ("restaurant_id", "order_sequence_number") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_892cef7cd3962597f0a1c00679" ON "orders" ("payment_status") `);
    await queryRunner.query(`CREATE INDEX "IDX_c134d148ae4c3ce4d094c68db3" ON "merchant_staff" ("restaurant_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_f352983724f663a30afcf73c88" ON "staff_fcm_tokens" ("merchant_staff_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_a02030a25506b8e2b1d21ad0a2" ON "staff_fcm_tokens" ("token") `);
    await queryRunner.query(`CREATE INDEX "IDX_2d0e434f89f68688e0845d6639" ON "chat_conversations" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_7f9e5689ad3d7f45172fc156fb" ON "chat_conversations" ("user_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_a954cc97827e9ec3a22a923169" ON "user-addresses" ("user_id", "is_temporary") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_61f1d453ca3c6f56322e44104a" ON "user-addresses" ("latitude", "longitude") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b39d7c4712fd9a0739e034919f" ON "user-addresses" ("user_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d544d2bc07b3107ac0dde71d35" ON "user_favourite_restaurants" ("restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a6ea8735a2b5ce2f792ff7196" ON "user_favourite_restaurants" ("user_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_54b296fa045ada3fc08af06bb8" ON "restaurants" ("total_reviews") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_4ff0af8a480bd8ad020ea03123" ON "restaurants" ("brand_id", "active_at") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_f46069b19d64ec43ff6b24d390" ON "restaurants" ("star_rated") `);
    await queryRunner.query(`CREATE INDEX "IDX_5af4db15acb151b8ebc046a6b7" ON "restaurants" ("price_range") `);
    await queryRunner.query(`CREATE INDEX "IDX_93448093be47625ee44fef5240" ON "restaurants" ("total_orders_sold") `);
    await queryRunner.query(`CREATE INDEX "IDX_a98ed620228a82ee3f59d57de4" ON "restaurants" ("schedule_active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_75eb8d9c79834a4a34b7fa5eb4" ON "restaurants" ("active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_6de2961809c2a7f90f20d45d7e" ON "restaurants" ("latitude") `);
    await queryRunner.query(`CREATE INDEX "IDX_f2d957e44b59ca1e7f45613c08" ON "restaurants" ("longitude") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_3bd1ca14eecd347cd0ae6ae169" ON "restaurant_available_schedules" ("restaurant_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_c0dbeb41e692716e3797485bbf" ON "reconciliations" ("end_time") `);
    await queryRunner.query(`CREATE INDEX "IDX_a0fef43e682b6aeb98b98ea816" ON "reconciliations" ("start_time") `);
    await queryRunner.query(`CREATE INDEX "IDX_b1d1ee96ffadef2956fd1d94ae" ON "reconciliations" ("bank_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_0543c28efa565975fd17a8bc7b" ON "reconciliations" ("settlement_plan") `);
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_9b9626d392ff967d6715178a3e" ON "menus" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_6b878682f9c53cbfe804ae288c" ON "menus" ("active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_bcd4a935c967cc9c20e770d1e6" ON "menus" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_ec73ac3da075000d0b7cbfafe4" ON "menu_sections" ("restaurant_id") `);
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_3bbc3e40de7b87601f907cf196" ON "menu_sections" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_216807fa8aeb465c9715e2d1cd" ON "menu_sections" ("active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_a4b2bf594216fc1e0a570dd02a" ON "menu_sections" ("schedule_active_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_806e6b8c727b7b60558b966df4" ON "menu_section_available_schedules" ("menu_section_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_8d1ee4780bf64ae94cbf3e5370" ON "menu_items" ("restaurant_id") `);
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_42b75f4146dd6c5facd0369007" ON "menu_items" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_ca6f3ba1951e27c19e27c229f8" ON "menu_items" ("active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_0e7c37bcce8087e08311f7ffa5" ON "menu_items" ("schedule_active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_b9ec5028095e6f37875372bccd" ON "menu_items" ("published_name") `);
    await queryRunner.query(`CREATE INDEX "IDX_ba96bbd861b8b0b4c2d2280a45" ON "menu_items" ("total_orders_sold") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_4c32753d45094d0f2f7bf05bef" ON "menu_item_option_groups" ("restaurant_id") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_2988f46a1a98b221a5ffcc641b" ON "menu_item_option_groups" ("code") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_3f3748c03ffce9383bc4760e1f" ON "ingredients" ("restaurant_id") `);
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_291910d4875ae08b2202f08eff" ON "ingredients" ("code") `);
    await queryRunner.query(`CREATE INDEX "IDX_8b44b2384fd44f07294e83b88b" ON "geofencing" ("shipping_fee") `);
    await queryRunner.query(`CREATE INDEX "IDX_651a142582b49f73fcaed66772" ON "geofencing" ("restaurant_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_51cda18866e732ce9138ee3e01" ON "brands" ("active_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_359b53d73273490ee0f0afda47" ON "merchant_accounts" ("active_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_b132a6f2a24dabfce5bd3e8d5d" ON "merchant_accounts" ("owner_merchant_user_id") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_6be527e7fe4a095222ee917cdf" ON "merchant_users" ("active_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_09df5343f9d0f4e24c320f4e47" ON "user_change_logs" ("user_id", "change_type") `,
    );
    await queryRunner.query(`CREATE INDEX "IDX_b32ebc7389b31249cec583b622" ON "user_change_logs" ("user_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_2aac7de7c15294c7f90a51d065" ON "users" ("phoneCountryCode") `);
    await queryRunner.query(`CREATE INDEX "IDX_869ca568c4ec52322f1681b1a3" ON "user_fcm_tokens" ("user_id") `);
    await queryRunner.query(`CREATE INDEX "IDX_9acd2767cf71e4107b4dfd3fcb" ON "user_fcm_tokens" ("token") `);
  }
}
