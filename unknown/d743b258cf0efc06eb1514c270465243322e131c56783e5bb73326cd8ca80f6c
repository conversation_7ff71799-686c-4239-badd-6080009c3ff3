import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

@Entity('bank_accounts')
export class BankAccount extends BaseEntity {
  @Column({ name: 'bank_name', type: 'varchar' })
  bankName: string;

  @Column({ name: 'account_number', type: 'varchar' })
  accountNumber: string;

  @Column({ name: 'account_holder', type: 'varchar' })
  accountHolder: string;

  @Column({ name: 'bank_branch', type: 'varchar' })
  bankBranch: string;

  @Column({ name: 'branch_address', type: 'varchar' })
  branchAddress: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', nullable: true, type: 'uuid' })
  restaurantId?: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'brand_id', nullable: true, type: 'uuid' })
  brandId?: string | null;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant?: WrapperType<Restaurant>;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brand_id' })
  brand?: WrapperType<Brand>;
}
