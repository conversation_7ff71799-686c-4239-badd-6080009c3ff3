import { Column, <PERSON>tity, Index, JoinC<PERSON>umn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { User } from '@/modules/users/entities/user.entity';

import { OnepayVoidRequest } from './onepay-void-request.entity';

@Entity('onepay_order_requests')
@Index(['updatedAt', 'txnResponseCode', 'drExists'], { where: 'deleted_at IS NULL' })
@Index(['updatedAt'], { where: 'deleted_at IS NULL' })
export class OnepayOrderRequest extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'order_id', type: 'uuid' })
  orderId: string;

  @Column({ name: 'order_code', type: 'varchar' })
  orderCode: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Column({ name: 'currency', type: 'varchar' })
  currency: string;

  @Column({ name: 'locale', type: 'varchar' })
  locale: string;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'merch_txn_ref', type: 'varchar' })
  merchTxnRef: string;

  @Column({ name: 'order_info', type: 'varchar' })
  orderInfo: string;

  @Column({ name: 'amount', type: 'integer' })
  amount: number;

  @Column({ name: 'ticket_no', type: 'varchar' })
  ticketNo: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'is_refunded', type: 'boolean', default: false })
  isRefunded: boolean;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'dr_exists', type: 'boolean', default: true })
  drExists: boolean;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'txn_response_code', type: 'varchar', nullable: true })
  txnResponseCode?: string;

  @OneToMany(() => OnepayVoidRequest, (voidRequest) => voidRequest.orgMerchTxnRef)
  voidRequests?: WrapperType<OnepayVoidRequest>[];

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: WrapperType<User>;
}
