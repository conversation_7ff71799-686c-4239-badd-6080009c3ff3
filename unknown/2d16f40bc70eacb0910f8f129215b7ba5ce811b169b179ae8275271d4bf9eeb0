import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';

import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';

import { MenuSection } from './menu-section.entity';

@Entity('mapping_menu_sections_menu_items')
@Index(['menuSectionId', 'menuItemId', 'price'])
export class MappingMenuSectionMenuItem {
  @Index()
  @PrimaryColumn({ name: 'menu_section_id', type: 'uuid' })
  menuSectionId: string;

  @Index()
  @PrimaryColumn({ name: 'menu_item_id', type: 'uuid' })
  menuItemId: string;

  @Column({ name: 'position', type: 'int', default: 0 })
  position: number;

  @Column({
    name: 'price',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    transformer: new ColumnNumericTransformer(),
  })
  price?: number | null;

  @ManyToOne(() => MenuSection, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_section_id' })
  menuSection: WrapperType<MenuSection>;

  @ManyToOne(() => MenuItem, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_item_id' })
  menuItem: WrapperType<MenuItem>;
}
