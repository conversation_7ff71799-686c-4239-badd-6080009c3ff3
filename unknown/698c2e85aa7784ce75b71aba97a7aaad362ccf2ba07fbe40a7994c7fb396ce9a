import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { MenuSection } from './menu-section.entity';

@Entity('menu_section_available_schedules')
export class MenuSectionAvailableSchedule extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'menu_section_id', type: 'uuid' })
  menuSectionId: string;

  @ManyToOne(() => MenuSection, (menuSection) => menuSection.availableSchedule, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'menu_section_id' })
  menuSection: WrapperType<MenuSection>;

  // Day of week (0=Sunday, 1=Monday, ..., 6=Saturday)
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'day', type: 'int' })
  day: number;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'start', type: 'time', default: '00:00:00' })
  start: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'end', type: 'time', default: '23:59:59' })
  end: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'is_all_day', type: 'boolean', default: false })
  isAllDay: boolean;
}
