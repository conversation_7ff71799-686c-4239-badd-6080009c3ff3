import { IsEnum, IsNotEmpty, IsOptional, IsUUID, ValidateIf } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

import { SettlementPlan } from '../enums/settlement-plan.enum';

export class UpsertReconciliationDto {
  @ApiProperty({ description: 'Restaurant ID' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({ description: 'Settlement plan', enum: SettlementPlan })
  @IsNotEmpty()
  @IsEnum(SettlementPlan)
  settlementPlan: SettlementPlan;

  @ApiProperty({ description: 'Bank account ID', required: false })
  @IsOptional()
  @ValidateIf((_, value) => value !== null)
  @IsUUID()
  bankId?: string | null;
}
