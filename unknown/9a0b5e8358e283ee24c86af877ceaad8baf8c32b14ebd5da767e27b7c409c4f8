import { Queue } from 'bullmq';

import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';

import { SendOtpSmsJob } from './sms.processor';

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);

  constructor(@InjectQueue('sms') private emailQueue: Queue<SendOtpSmsJob>) {}

  async queueOtpSms(to: string, code: string, otpExpiryMinutes: number): Promise<void> {
    try {
      const jobId = `sms-otp-${to}-${Date.now()}`;
      await this.emailQueue.add(
        'send-otp-sms',
        {
          to,
          code,
          otpExpiryMinutes,
        },
        {
          jobId,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10,
          removeOnFail: 5,
        },
      );
      this.logger.log(`OTP email queued for ${to} with job ID: ${jobId}`);
    } catch (error) {
      this.logger.error(`Failed to queue OTP email for ${to}:`, error);
      throw new Error(`Failed to queue email: ${error.message}`);
    }
  }
}
