import { Response } from 'express';
import { DataSource } from 'typeorm';

import { OtpService } from '@/modules/otp/otp.service';
import { UserAddressesService } from '@/modules/user-addresses/user-addresses.service';
import { User } from '@/modules/users/entities/user.entity';
import { UsersService } from '@/modules/users/users.service';
import { BadRequestException, Injectable, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';

import { AuthUserService } from './auth-user.service';
import {
  SendEmailOtpDto,
  SendPhoneOtpDto,
  UpdateProfileDto,
  VerifyEmailOtpDto,
  VerifyPhoneOtpDto,
} from './dtos/onboarding.dto';

@Injectable()
export class OnboardingService {
  private readonly logger = new Logger(OnboardingService.name);

  constructor(
    private otpService: OtpService,
    private usersService: UsersService,
    private authUserService: AuthUserService,
    private userAddressesService: UserAddressesService,

    private dataSource: DataSource,
  ) {}

  async sendPhoneOtp(sendPhoneOtpDto: SendPhoneOtpDto) {
    const { phone, phoneCountryCode } = sendPhoneOtpDto;
    try {
      const data = await this.otpService.sendPhoneOtp(phone, phoneCountryCode);
      return {
        success: true,
        message: 'OTP sent to phone successfully',
        data,
      };
    } catch (error) {
      this.logger.error(`Failed to send phone OTP to ${phone}:`, error);
      throw new BadRequestException('Failed to send phone OTP');
    }
  }

  async verifyPhoneOtp(verifyPhoneOtpDto: VerifyPhoneOtpDto) {
    const { phone, otp, phoneCountryCode } = verifyPhoneOtpDto;
    const isValid = await this.otpService.verifyPhoneOtp(phone, otp, phoneCountryCode);

    if (!isValid) {
      throw new UnauthorizedException('The OTP is incorrect. Please try again');
    }

    return this.authUserService.generateAccessTokenToVerifyPhone(phone, phoneCountryCode);
  }

  async sendEmailOtp(sendEmailOtpDto: SendEmailOtpDto) {
    const { email, phoneVerificationToken } = sendEmailOtpDto;
    await this.authUserService.verifyPhoneVerificationToken(phoneVerificationToken);
    try {
      const data = await this.otpService.sendEmailOtp(email);
      return {
        success: true,
        message: 'OTP sent to email successfully',
        data,
      };
    } catch (error) {
      this.logger.error(`Failed to send email OTP to ${email}:`, error);
      throw new BadRequestException('Failed to send email OTP');
    }
  }

  async verifyEmailOtpAndLogin(verifyEmailOtpDto: VerifyEmailOtpDto, response: Response) {
    const { email, otp, phoneVerificationToken } = verifyEmailOtpDto;
    const payload = await this.authUserService.verifyPhoneVerificationToken(phoneVerificationToken);
    const { phone, phoneCountryCode } = payload;

    const isValid = await this.otpService.verifyEmailOtp(email, otp);

    if (!isValid) {
      throw new UnauthorizedException('The OTP is incorrect. Please try again');
    }

    const user = await this.dataSource.transaction(async (manager) => {
      let user = await manager.findOne(User, { where: { email } });
      const userData = { email, phone, phoneCountryCode };
      if (!user) {
        // Create new user
        user = await this.usersService.createNewUser(userData, manager);
      } else {
        if (user.phone !== phone || user.phoneCountryCode !== phoneCountryCode) {
          user = await this.usersService.updateUserPhone(user, userData, manager);
        }
      }

      return user;
    });
    // Clear phone verification token after successful login
    await this.authUserService.clearPhoneVerificationToken(phoneVerificationToken);

    return this.authUserService.login(user, response);
  }

  async fakeLogin(email: string, response: Response) {
    const user = await this.usersService.findOneByEmail(email);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.authUserService.login(user, response);
  }

  async updateProfile(userId: string, updateUserNameDto: UpdateProfileDto) {
    const { firstName, lastName, language } = updateUserNameDto;
    try {
      const updatedUser = await this.usersService.updateProfile(userId, { firstName, lastName, language });
      const defaultAddress = await this.userAddressesService.getDefaultAddress(userId);
      return {
        ...updatedUser,
        hasAddress: !!defaultAddress,
        defaultAddress,
      };
    } catch (error) {
      this.logger.error(`Failed to update user profile for ${userId}:`, error);
      throw new BadRequestException('Failed to update profile');
    }
  }
}
