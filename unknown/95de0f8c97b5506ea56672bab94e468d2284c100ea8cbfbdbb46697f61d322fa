import { Request } from 'express';

import { getClientIP } from '@/helpers/network';
import { OtpService } from '@/modules/otp/otp.service';
import { UsersService } from '@/modules/users/users.service';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';

import {
  RequestUpdateEmailDto,
  RequestUpdatePhoneDto,
  VerifyUpdateEmailDto,
  VerifyUpdatePhoneDto,
} from './dtos/update-profile.dto';

@Injectable()
export class ProfileUpdateService {
  private readonly logger = new Logger(ProfileUpdateService.name);

  constructor(
    private readonly otpService: OtpService,
    private readonly usersService: UsersService,
  ) {}

  private async validateNewEmail(userId: string, newEmail: string) {
    // Check if email already exists
    const currentUser = await this.usersService.findById(userId);
    if (currentUser.email === newEmail) {
      throw new BadRequestException('New email is the same as current email');
    }

    const existingUser = await this.usersService.findOneByEmail(newEmail);
    if (existingUser && existingUser.id !== userId) {
      throw new BadRequestException('Email already exists');
    }

    return currentUser;
  }

  async requestUpdateEmail(userId: string, requestUpdateEmailDto: RequestUpdateEmailDto) {
    const { newEmail } = requestUpdateEmailDto;

    await this.validateNewEmail(userId, newEmail);

    try {
      return await this.otpService.sendUpdateEmailOtp(newEmail);
    } catch (error) {
      this.logger.error(`Failed to send update email OTP for ${newEmail}:`, error);
      throw new BadRequestException('Failed to send OTP email');
    }
  }

  async verifyUpdateEmail(userId: string, verifyUpdateEmailDto: VerifyUpdateEmailDto, request: Request) {
    const { newEmail, otp } = verifyUpdateEmailDto;

    const currentUser = await this.validateNewEmail(userId, newEmail);

    const isValid = await this.otpService.verifyUpdateEmailOtp(newEmail, otp);
    if (!isValid) {
      throw new BadRequestException('The OTP is incorrect. Please try again');
    }

    try {
      const userAgent = request.get('User-Agent');
      const ipAddress = getClientIP(request, this.logger);
      const updatedUser = await this.usersService.updateUserEmail(currentUser, newEmail, {
        userAgent,
        ipAddress,
      });

      return {
        message: 'Email updated successfully',
        user: updatedUser,
      };
    } catch (error) {
      this.logger.error(`Failed to update email for user ${userId}:`, error);
      throw new BadRequestException('Failed to update email');
    }
  }

  private async validateNewPhone(userId: string, newPhone: string, newPhoneCountryCode: string) {
    // Check if phone already exists
    const currentUser = await this.usersService.findById(userId);
    if (currentUser.phone === newPhone && currentUser.phoneCountryCode === newPhoneCountryCode) {
      throw new BadRequestException('New phone number is the same as current phone number');
    }

    const existingUser = await this.usersService.findOneByPhone(newPhone);
    if (existingUser && existingUser.id !== userId) {
      throw new BadRequestException('Phone number already exists');
    }

    return currentUser;
  }

  async requestUpdatePhone(userId: string, requestUpdatePhoneDto: RequestUpdatePhoneDto) {
    const { newPhone, newPhoneCountryCode } = requestUpdatePhoneDto;

    await this.validateNewPhone(userId, newPhone, newPhoneCountryCode);

    try {
      return await this.otpService.sendUpdatePhoneOtp(newPhone, newPhoneCountryCode);
    } catch (error) {
      this.logger.error(`Failed to send update phone OTP for ${newPhone}:`, error);
      throw new BadRequestException('Failed to send OTP SMS');
    }
  }

  async verifyUpdatePhone(userId: string, verifyUpdatePhoneDto: VerifyUpdatePhoneDto, request: Request) {
    const { newPhone, newPhoneCountryCode, otp } = verifyUpdatePhoneDto;

    const currentUser = await this.validateNewPhone(userId, newPhone, newPhoneCountryCode);

    const isValid = await this.otpService.verifyUpdatePhoneOtp(newPhone, otp, newPhoneCountryCode);
    if (!isValid) {
      throw new BadRequestException('The OTP is incorrect. Please try again');
    }

    try {
      const userAgent = request.get('User-Agent');
      const ipAddress = getClientIP(request, this.logger);
      const updatedUser = await this.usersService.updateUserPhoneNumber(currentUser, newPhone, newPhoneCountryCode, {
        userAgent,
        ipAddress,
      });

      return {
        message: 'Phone number updated successfully',
        user: updatedUser,
      };
    } catch (error) {
      this.logger.error(`Failed to update phone for user ${userId}:`, error);
      throw new BadRequestException('Failed to update phone number');
    }
  }
}
