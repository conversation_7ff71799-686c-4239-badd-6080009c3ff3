import { Reconciliation } from '@/modules/reconciliations/entities/reconciliation.entity';
import { SettlementPlan } from '@/modules/reconciliations/enums/settlement-plan.enum';

import { calculateNextReconciliationPeriod, getDayjsTimeWithTz } from '../reconciliations.helper';

export class ReconciliationResponseDto {
  id: string;
  restaurantId: string;
  settlementPlan: SettlementPlan;
  bankId: string | null;
  nextSettlementPlan: SettlementPlan | null;
  currentPeriod?: {
    startDate: Date;
    endDate: Date;
  };
  nextPeriod?: {
    startDate: Date;
    endDate: Date;
  };
  restaurant?: any;
  bankAccount?: any;

  static fromEntity(reconciliation: Reconciliation): ReconciliationResponseDto {
    const response = new ReconciliationResponseDto();
    response.id = reconciliation.id;
    response.restaurantId = reconciliation.restaurantId;
    response.settlementPlan = reconciliation.settlementPlan;
    response.bankId = reconciliation.bankId;
    response.nextSettlementPlan = reconciliation.nextSettlementPlan;
    response.restaurant = reconciliation.restaurant;
    response.bankAccount = reconciliation.bankAccount;

    if (!reconciliation.startTime || !reconciliation.endTime) return response;

    const currentPeriodStart = getDayjsTimeWithTz(reconciliation.startTime).toDate();
    const currentPeriodEnd = getDayjsTimeWithTz(reconciliation.endTime).toDate();
    response.currentPeriod = {
      startDate: currentPeriodStart,
      endDate: currentPeriodEnd,
    };

    if (!reconciliation.nextSettlementPlan) return response;

    // Calculate next period
    const nextPeriodEnd = calculateNextReconciliationPeriod(
      currentPeriodEnd,
      reconciliation.nextSettlementPlan,
    ).endDate;

    response.nextPeriod = {
      startDate: currentPeriodEnd,
      endDate: nextPeriodEnd,
    };

    return response;
  }
}
