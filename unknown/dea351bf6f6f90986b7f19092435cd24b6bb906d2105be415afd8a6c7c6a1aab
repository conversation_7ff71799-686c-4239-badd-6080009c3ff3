import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { User } from './user.entity';

export enum ChangeType {
  EMAIL = 'email',
  PHONE = 'phone',
  OTHER_INFO = 'otherInfo',
}

@Entity('user_change_logs')
@Index(['userId', 'changeType'], { where: 'deleted_at IS NULL' })
export class UserChangeLog extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'change_type', type: 'varchar' })
  changeType: ChangeType;

  @Column({ name: 'before_data', type: 'jsonb' })
  beforeData: Record<string, any>;

  @Column({ name: 'after_data', type: 'jsonb' })
  afterData: Record<string, any>;

  @Column({ name: 'changed_by_user_id', type: 'uuid', nullable: true })
  changedByUserId: string | null;

  @Column({ name: 'user_agent', type: 'varchar', nullable: true })
  userAgent: string | null;

  @Column({ name: 'ip_address', type: 'inet', nullable: true })
  ipAddress: string | null;
}
