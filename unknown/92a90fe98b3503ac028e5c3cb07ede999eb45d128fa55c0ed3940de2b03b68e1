import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeFieldVatToBrand1753945436562 implements MigrationInterface {
  name = 'ChangeFieldVatToBrand1753945436562';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "include_vat"`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "include_vat" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "include_vat"`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "include_vat" boolean NOT NULL DEFAULT false`);
  }
}
