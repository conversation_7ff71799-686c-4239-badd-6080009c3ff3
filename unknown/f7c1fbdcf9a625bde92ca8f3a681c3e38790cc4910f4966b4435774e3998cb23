import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldIncludeVatRestaurant1753891629927 implements MigrationInterface {
  name = 'AddFieldIncludeVatRestaurant1753891629927';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "include_vat" boolean NOT NULL DEFAULT false`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "include_vat"`);
  }
}
