import { Logger as TypeOrmLogger, QueryRunner } from 'typeorm';

import { Logger as NestLogger } from '@nestjs/common';

export class TypeOrmSlowQueryLogger implements TypeOrmLogger {
  private readonly logger = new NestLogger('TypeOrmSlowQuery');
  private readonly slowQueryThreshold: number;

  constructor(slowQueryThreshold: number = 1000) {
    this.slowQueryThreshold = slowQueryThreshold; // Default: 1 second
    this.logger.localInstance.setLogLevels?.(['error', 'warn', 'debug']);
  }

  logQuery(query: string, parameters?: any[], _queryRunner?: QueryRunner) {
    // Only log if general logging is enabled and we're in development\
    if (process.env.NODE_ENV !== 'development') return;
    this.logger.debug(`Query: ${query}${parameters ? ` Parameters: ${JSON.stringify(parameters)}` : ''}`);
    // this.logger.debug(`Query: ${this.formatQuery(query, parameters)}`);
  }

  logQueryError(error: string | Error, query: string, parameters?: any[], _queryRunner?: QueryRunner) {
    this.logger.error(`Query Failed: ${query}${parameters ? ` Parameters: ${JSON.stringify(parameters)}` : ''}`);
    this.logger.error(`Error: ${error}`);
  }

  logQuerySlow(time: number, query: string, parameters?: any[], _queryRunner?: QueryRunner) {
    if (time >= this.slowQueryThreshold) {
      this.logger.warn(`🐌 SLOW QUERY DETECTED - Execution Time: ${time}ms`);

      this.logger.debug(`Query: ${query}${parameters ? ` Parameters: ${JSON.stringify(parameters)}` : ''}`);
      this.logger.warn(`Threshold: ${this.slowQueryThreshold}ms`);
      this.logger.warn('---'.repeat(20));
    }
  }

  logSchemaBuild(message: string, _queryRunner?: QueryRunner) {
    this.logger.log(`Schema Build: ${message}`);
  }

  logMigration(message: string, _queryRunner?: QueryRunner) {
    this.logger.log(`Migration: ${message}`);
  }

  log(level: 'log' | 'info' | 'warn', message: any, _queryRunner?: QueryRunner) {
    switch (level) {
      case 'log':
        this.logger.log(message);
        break;
      case 'info':
        this.logger.log(message);
        break;
      case 'warn':
        this.logger.warn(message);
        break;
    }
  }

  private formatQuery(query: string, parameters?: any[]): string {
    if (!parameters) return query;
    return query.replace(/\$(\d+)/g, (_, index) => {
      const param = parameters[Number(index) - 1];
      return this.formatParam(param);
    });
  }

  private formatParam(param: any): string {
    if (param === null || param === undefined) return 'NULL';
    if (typeof param === 'string') return `'${param.replace(/'/g, "''")}'`; // escape single quotes
    if (typeof param === 'number' || typeof param === 'boolean') return param.toString();
    if (param instanceof Date) return `'${param.toISOString()}'`;
    if (Array.isArray(param)) return `ARRAY[${param.map((p) => this.formatParam(p)).join(', ')}]`;
    try {
      return `'${JSON.stringify(param)}'`;
    } catch {
      return `'${String(param)}'`;
    }
  }
}
