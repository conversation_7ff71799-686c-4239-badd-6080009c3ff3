import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Order } from '@/modules/orders/entities/order.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { User } from '@/modules/users/entities/user.entity';

@Entity('chat_conversations')
export class ChatConversation extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ name: 'order_id', type: 'uuid' })
  orderId: string;

  @Column({ name: 'last_message_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  lastMessageAt: Date;

  @Column({ name: 'last_message_content', nullable: true, type: 'text' })
  lastMessageContent?: string | null;

  @Column({ name: 'user_last_seen_at', nullable: true, type: 'timestamptz' })
  userLastSeenAt?: Date | null;

  @Column({ name: 'restaurant_last_seen_at', nullable: true, type: 'timestamptz' })
  restaurantLastSeenAt?: Date | null;

  @Column({ name: 'unread_count_user', default: 0, type: 'integer' })
  unreadCountUser: number;

  @Column({ name: 'unread_count_restaurant', default: 0, type: 'integer' })
  unreadCountRestaurant: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: WrapperType<User>;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  @ManyToOne(() => Order)
  @JoinColumn({ name: 'order_id' })
  order: WrapperType<Order>;
}
