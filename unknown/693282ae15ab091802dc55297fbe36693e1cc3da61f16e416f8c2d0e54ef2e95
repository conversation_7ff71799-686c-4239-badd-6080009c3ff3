import { Column, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON>umn, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';

import { MenuItemOptionGroup } from '@/modules/menu-item-option-groups/entities/menu-item-option-group.entity';

import { MenuItem } from './menu-item.entity';

@Entity('mapping_menu_items_menu_item_option_groups')
export class MappingMenuItemMenuItemOptionGroup {
  @Index()
  @PrimaryColumn({ name: 'menu_item_id', type: 'uuid' })
  menuItemId: string;

  @Index()
  @PrimaryColumn({ name: 'menu_item_option_group_id', type: 'uuid' })
  menuItemOptionGroupId: string;

  @Column({ name: 'position', type: 'int', default: 0 })
  position: number;

  @ManyToOne(() => MenuItem, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_item_id' })
  menuItem: WrapperType<MenuItem>;

  @ManyToOne(() => MenuItemOptionGroup, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_item_option_group_id' })
  menuItemOptionGroup: WrapperType<MenuItemOptionGroup>;
}
