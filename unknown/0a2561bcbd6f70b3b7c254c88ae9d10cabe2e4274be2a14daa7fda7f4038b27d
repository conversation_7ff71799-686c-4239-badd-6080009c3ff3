import { User } from '@/common/decorators/user.decorator';
import { OptionalAuth } from '@/modules/auth/decorators/optional-auth.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Controller, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { MenuItem } from '../entities/menu-item.entity';
import { MenuItemsService } from '../menu-items.service';

@ApiTags('(User) Menu Items')
@Controller('user/menu-items')
@Roles({ userType: UserType.USER, role: '*' })
@OptionalAuth()
export class UserMenuItemsController {
  constructor(private readonly menuItemsService: MenuItemsService) {}

  @Get(':restaurantId/:menuSectionId/:menuItemId')
  userFindOne(
    @Param('restaurantId', ParseUUIDPipe) restaurantId: string,
    @Param('menuSectionId', ParseUUIDPipe) menuSectionId: string,
    @Param('menuItemId', ParseUUIDPipe) menuItemId: string,
    @User() user: UserJwtInfo,
  ): Promise<MenuItem> {
    return this.menuItemsService.findOneByUser(restaurantId, menuSectionId, menuItemId, user);
  }
}
