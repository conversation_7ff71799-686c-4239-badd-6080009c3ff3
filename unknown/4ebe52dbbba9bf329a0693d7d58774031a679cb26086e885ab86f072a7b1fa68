import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateBankAccountDto {
  @ApiProperty({ description: 'Bank name' })
  @IsNotEmpty()
  @IsString()
  bankName: string;

  @ApiProperty({ description: 'Account number' })
  @IsNotEmpty()
  @IsString()
  accountNumber: string;

  @ApiProperty({ description: 'Account holder' })
  @IsNotEmpty()
  @IsString()
  accountHolder: string;

  @ApiProperty({ description: 'Bank branch' })
  @IsNotEmpty()
  @IsString()
  bankBranch: string;

  @ApiProperty({ description: 'Branch address' })
  @IsNotEmpty()
  @IsString()
  branchAddress: string;

  @ApiPropertyOptional({ description: 'Restaurant ID (if linked to a restaurant)' })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;

  @IsOptional()
  @IsUUID()
  brandId?: string;
}
