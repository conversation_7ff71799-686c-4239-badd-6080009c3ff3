import { Exclude } from 'class-transformer';
import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';

import { OrderItem } from './order-item.entity';

@Entity('order_item_options')
@Index(['orderItemId', 'menuItemOptionGroupId', 'menuItemOptionId'], {
  unique: true,
  where: 'deleted_at IS NULL',
})
export class OrderItemOption extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'order_item_id', type: 'uuid' })
  orderItemId: string;

  @Column({ name: 'menu_item_option_group_id', type: 'uuid' })
  menuItemOptionGroupId: string;

  @Column({ name: 'menu_item_option_id', type: 'uuid' })
  menuItemOptionId: string;

  @Column({ name: 'amount', default: 1, type: 'integer' })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  price: number;

  @Column({
    name: 'tax_amount',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
    transformer: new ColumnNumericTransformer(),
  })
  taxAmount: number;

  // Duplicate information
  @Column({ name: 'option_group_name', type: 'varchar' })
  optionGroupName: string;

  @Exclude()
  @Column({ name: 'option_group_name_en', type: 'varchar', nullable: true })
  optionGroupNameEn?: string | null;

  @Exclude()
  @Column({ name: 'option_group_name_vi', type: 'varchar', nullable: true })
  optionGroupNameVi?: string | null;

  @Column({ name: 'option_name', type: 'varchar' })
  optionName: string;

  @Exclude()
  @Column({ name: 'option_name_en', type: 'varchar', nullable: true })
  optionNameEn?: string | null;

  @Exclude()
  @Column({ name: 'option_name_vi', type: 'varchar', nullable: true })
  optionNameVi?: string | null;

  @Column({ name: 'is_alcohol', type: 'boolean', default: false })
  isAlcohol: boolean;

  @ManyToOne(() => OrderItem, (orderItem) => orderItem.orderItemOptions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_item_id' })
  orderItem: WrapperType<OrderItem>;
}
