export enum OrderStatus {
  NEW = 'new',
  MODIFIED = 'modified',
  MODIFIED_ACCEPTED = 'modified_accepted',
  IN_KITCHEN = 'in_kitchen',
  IN_KITCHEN_OVERDUE = 'in_kitchen_overdue',
  IN_DELIVERY = 'in_delivery',
  IN_DELIVERY_OVERDUE = 'in_delivery_overdue',
  PAYING = 'paying',
  NOT_DELIVERED = 'not_delivered',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  UNFULFILLED = 'unfulfilled',
}

export enum PaymentStatus {
  FAILED = 'failed',
  PAID = 'paid',
  REFUNDED = 'refunded',
}

export enum PaymentMethod {
  CASH = 'cash',
  CREDIT_CARD = 'credit_card',
}

export enum NotificationType {
  NEW_ORDER = 'new_order',
  ORDER_CONFIRMED = 'order_confirmed',
  ORDER_IN_KITCHEN = 'order_in_kitchen',
  ORDER_READY_FOR_DELIVERY = 'order_ready_for_delivery',
  ORDER_IN_DELIVERY = 'order_in_delivery',
  ORDER_DELIVERED = 'order_delivered',
  ORDER_MODIFIED = 'order_modified',
  ORDER_CANCELLED = 'order_cancelled',
  ORDER_UNFULFILLED = 'order_unfulfilled',
  DELIVERY_ETA_UPDATED = 'delivery_eta_updated',
  ORDER_OVERDUE = 'order_overdue',
  DELIVERY_ISSUE = 'delivery_issue',
  DONT_MISS_ORDER = 'dont_miss_order',
}

// Status groups for filtering and counting
export enum OrderStatusGroup {
  NEEDS_ACTION = 'needs_action',
  IN_PROCESS = 'in_process',
  COMPLETED = 'completed',
}

export enum OrderCancelReason {
  OVERTIME_ACCEPT_NEW_ORDER = 'overtime_accept_new_order',
  OVERTIME_ACCEPT_MODIFIED_ORDER = 'overtime_accept_modified_order',
  OVERTIME_ACCEPT_MODIFIED_ACCEPTED_ORDER = 'overtime_accept_modified_accepted_order',
  USER_REJECTED_MODIFICATION = 'user_rejected_modification',
  RESTAURANT_REJECTED = 'restaurant_rejected',
  USER_CANCELLED = 'user_cancelled',
  PAYMENT_FAILED = 'payment_failed',
}
