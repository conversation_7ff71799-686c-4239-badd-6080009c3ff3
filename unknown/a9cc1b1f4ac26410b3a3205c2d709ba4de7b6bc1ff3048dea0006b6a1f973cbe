import { User } from '@/common/decorators/user.decorator';
import { Body, Controller, Delete, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { Roles } from '../../auth/decorators/roles.decorator';
import { UserType } from '../../auth/enums/user-type.enum';
import { FCMService } from '../../shared/fcm/fcm.service';
import { AddFCMTokenDto, RemoveFCMTokenDto } from '../dtos/fcm-token.dto';

@ApiTags('(User) FCM Tokens')
@Controller('user/fcm-tokens')
@Roles({ userType: UserType.USER, role: '*' })
export class UsersController {
  constructor(private readonly fcmService: FCMService) {}

  @Post()
  @ApiOperation({ summary: 'Add FCM token for current user' })
  async addFCMToken(@User('id') userId: string, @Body() addFCMTokenDto: AddFCMTokenDto) {
    const fcmToken = await this.fcmService.addFCMToken(userId, addFCMTokenDto);
    return {
      message: 'FCM token added successfully',
      data: fcmToken,
    };
  }

  @Delete()
  @ApiOperation({ summary: 'Remove FCM token for current user' })
  async removeFCMToken(@User('id') userId: string, @Query() removeFCMTokenDto: RemoveFCMTokenDto) {
    await this.fcmService.removeFCMToken(userId, removeFCMTokenDto);
    return { message: 'FCM token removed successfully' };
  }
}
