import dayjs from 'dayjs';

import { SettlementPlan } from './enums/settlement-plan.enum';

const getUnitTime = () => {
  const unitEnv = process.env.RECOCILIATION_TIME_UNIT;
  switch (unitEnv) {
    case 'day':
      return 'day';
    case 'hour':
      return 'hour';
    case 'minute':
      return 'minute';
    default:
      return 'day';
  }
};

const unit = getUnitTime();

export const calculateNextReconciliationPeriod = (startTime: Date, settlementPlan: SettlementPlan) => {
  const currentPeriodStart = getDayjsTimeWithTz(startTime);
  const nextPeriodEnd = currentPeriodStart.add(Number(settlementPlan), unit).startOf(unit);
  return {
    startDate: currentPeriodStart.toDate(),
    endDate: nextPeriodEnd.toDate(),
  };
};

export const getDayjsTimeWithTz = (time?: Date) => {
  return dayjs(time).tz('Asia/Ho_Chi_Minh');
};
