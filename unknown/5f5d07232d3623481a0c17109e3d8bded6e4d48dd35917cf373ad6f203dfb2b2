import { Entity, Index, JoinColumn, ManyToOne, PrimaryColumn } from 'typeorm';

import { ReviewTag } from '@/modules/review-tags/entities/review-tag.entity';

import { RestaurantReview } from './restaurant-review.entity';

@Entity('mapping_restaurant_review_tags')
@Index(['restaurantReviewId', 'reviewTagId'], { unique: true }) // Prevent duplicate tags per review
export class MappingRestaurantReviewTag {
  @Index()
  @PrimaryColumn({ name: 'restaurant_review_id', type: 'uuid' })
  restaurantReviewId: string;

  @Index()
  @PrimaryColumn({ name: 'review_tag_id', type: 'uuid' })
  reviewTagId: string;

  @ManyToOne(() => RestaurantReview, (review) => review.mappingRestaurantReviewTags, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'restaurant_review_id' })
  restaurantReview?: WrapperType<RestaurantReview>;

  @ManyToOne(() => ReviewTag, (reviewTag) => reviewTag.mappingRestaurantReviewTags)
  @JoinColumn({ name: 'review_tag_id' })
  reviewTag?: WrapperType<ReviewTag>;
}
