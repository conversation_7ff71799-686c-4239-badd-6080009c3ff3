import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIndexForCommonField1753648838485 implements MigrationInterface {
  name = 'AddIndexForCommonField1753648838485';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX "IDX_514f153cff3ef351d4a57e3e9a" ON "user_fcm_tokens" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3c0a9d139782be53d6dbe27789" ON "user_fcm_tokens" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_e34d25b244d759b695c81ff22f" ON "user_fcm_tokens" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_3bfdfe806467b2560a3821755f" ON "users" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_71a0e730d3e050468cffa42acd" ON "users" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_073999dfec9d14522f0cf58cd6" ON "users" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_454df75392d6e23f453cb1efa3" ON "user_change_logs" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3ea6ebb2fff4a021393bf95af5" ON "user_change_logs" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_9b09f0385ad6259f4170d17639" ON "user_change_logs" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_4c23fc1294c1f1d7d37d8d056f" ON "user-addresses" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3c2ee42eecaa5196c3b69ea527" ON "user-addresses" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_16a2caab9bdd784e68265d9f44" ON "user-addresses" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_6002a84585068e7b46ed224fc9" ON "merchant_users" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cebe6e9ef8d6f83cdc093f29a0" ON "merchant_users" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_5c567422513bc84a5364bd2d49" ON "merchant_users" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_42003fdb8497eb02f42d15bfae" ON "merchant_accounts" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7d5025c080027789b4911d4064" ON "merchant_accounts" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_e7c9c60cc99ae871bdd2d2a773" ON "merchant_accounts" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_8e99465e1556dedd84b23adec1" ON "brands" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dc91f31bdff2b83aa566ed8eb0" ON "brands" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_386d9e84282e6bc84bf27ef8d9" ON "brands" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_0b4667f7214f7aa3ae5ccab345" ON "geofencing" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_90fc5e64ac29f4de00f704a511" ON "geofencing" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_ccc0e05e4fc0c6ad80d70b0b6b" ON "geofencing" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_f933695fb0f6fcb4e646aa5ef9" ON "ingredients" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_53e05eb0781650b76cf5536911" ON "ingredients" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_c9d6021ed7fd3344f03a0a4d2c" ON "ingredients" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_8cf4febb6b0e685b65da6b9a1c" ON "menu_item_option_groups" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8b9b6e9bc9c08bb08106f29425" ON "menu_item_option_groups" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a8dffcf59a0df2f02ee2f16a4" ON "menu_item_option_groups" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5a5b4f6a58be6cc5297a033942" ON "menu_items" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_03b7c4b422bd6db4373bee9236" ON "menu_items" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_41e9821a5d1b51b9884182bcc0" ON "menu_items" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_7c633b769fb4ea0cdec1975657" ON "menu_section_available_schedules" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8adb65a7700c42e41b87fd671a" ON "menu_section_available_schedules" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_072df1e1583f098db9cd3d07eb" ON "menu_section_available_schedules" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_84ab8ece3e53bf4befaa651859" ON "menu_sections" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f03dc32e659435d58558dc4bcf" ON "menu_sections" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_e1377fe39b40bb99bb0c2bcaf6" ON "menu_sections" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_6642e9bcbee8538c70dcae0d3d" ON "menus" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bb5fb4ff01d1f9ef913c38c140" ON "menus" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_05fbcebe65179ec1a36a4975d7" ON "menus" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_c7cc631123c0a7c8aca098c09a" ON "bank_accounts" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a152afbdaa87d6b0d72c8c22d" ON "bank_accounts" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_c815c753f9a36488e314c4cd57" ON "bank_accounts" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_a2e893c8c2915e29b4693a5dcb" ON "reconciliations" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_810bd6922e4fcab980cae85edd" ON "reconciliations" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_05338a97ae399a2db15ef253c9" ON "reconciliations" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_22d166a327386b29e1ef7e27e8" ON "restaurant_tags" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d93b27402fe286a93073cef445" ON "restaurant_tags" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_3c988ca558690538ca8a9c931c" ON "restaurant_tags" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_af85b3e934411845173148769e" ON "restaurant_available_schedules" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c041ba2187d5ec2f695daa7ad3" ON "restaurant_available_schedules" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9a0fd592e82969ba64d917b6a3" ON "restaurant_available_schedules" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4a3b63e2ec07e45c937a5dbc68" ON "restaurants" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8fa903affeb684dd76f7932140" ON "restaurants" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_eb6ec9ec5909e16e600360a441" ON "restaurants" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_2d4f7dc3be127de7ee4698626f" ON "user_favourite_restaurants" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6fcb53e6e5d142287659627567" ON "user_favourite_restaurants" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_30cb3939ac2abcc8c8ebaf3df9" ON "user_favourite_restaurants" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1dfccf41be2f3e820dacaf8fc3" ON "chat_conversations" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ff3b0ac13d3b663ca730387535" ON "chat_conversations" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_2493988e5e7a92d86834b19615" ON "chat_conversations" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_f8c0228a5aa332289fb0309ece" ON "order_customers" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cbc4530cd5021b052342d3a44e" ON "order_customers" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_4e3ef01f63307bfaa7f91b2f9b" ON "order_customers" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_0a384ed28a3f9f9859442b70f4" ON "order_item_options_original" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4ade87eedcd7060a58c1f82e81" ON "order_item_options_original" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_421d90c3933f487640b38bf398" ON "order_item_options_original" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3a9edb04d243fbed5770c1f7f1" ON "order_items_original" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bcb2ceb18f1cb156ac64bc89dc" ON "order_items_original" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_3807b6a5f6619228cf62cd4481" ON "order_items_original" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_51fc747e8c26dbe526d710edbd" ON "staff_fcm_tokens" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_92600adff7d1d2da4aa5b24863" ON "staff_fcm_tokens" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_829a1ddd92f9cc799385974bbd" ON "staff_fcm_tokens" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_66b01417968ad057c8b347b73f" ON "merchant_staff" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ed7dc74049eb1695cfe8605533" ON "merchant_staff" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_f87a4be76e57d041471dc95c32" ON "merchant_staff" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_4023fb8983b6e9afc6dd1bf44f" ON "order_item_options" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bf4c09d9591e0b4823e5f947ed" ON "order_item_options" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_e81f2dcea5b88695d573afc6d8" ON "order_item_options" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_06b03b0a3688a98c801602f86a" ON "order_items" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c3c58fc82087d86920f4c8a106" ON "order_items" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_bfd91f86461c497971a5d27bd2" ON "order_items" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_b942520144709ee331e18d7b44" ON "orders" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f170c0e92159ed8b20349939ae" ON "orders" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_09b0a39ef7c0b162f6a2f3c860" ON "orders" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d1b91ae8cdbcb16d1d5d908ec0" ON "restaurant_review_replies" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_20cdc3c43d37229ed53dcb1c23" ON "restaurant_review_replies" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e93de40b99d42a4e376fee2831" ON "restaurant_review_replies" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cb61f258681a9b85ce04739cf0" ON "restaurant_reviews" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1db3e61c56f2efe2801c2eddec" ON "restaurant_reviews" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_2dcda485d02e0fac6158a0f94a" ON "restaurant_reviews" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_ea4bd36b507c6d7fff15e149ab" ON "review_tags" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_cb069e13f6c65127c9d03a7f4c" ON "review_tags" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_38459db642582bd027aecf296c" ON "review_tags" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_64ad9c317ab118f449c027cf88" ON "user_payment_cards" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a16e1ae67f037d93470f8b3dcb" ON "user_payment_cards" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_02b9c0da8bfa233dfd94e11bf1" ON "user_payment_cards" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_1e9261ebc4f646f9aeb0d62434" ON "onepay_void_requests" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c486903d692c953aa31b55eb30" ON "onepay_void_requests" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_9d989fcf6aa4793ad8d30919fe" ON "onepay_void_requests" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d463dd48773762a0df14a7c5a0" ON "onepay_order_requests" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c546f49ee3b2c7ecf9644b592c" ON "onepay_order_requests" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_41b2ddc2db2359b08c9c7b488f" ON "onepay_order_requests" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_2518af7970bbf7cbb331c1dea5" ON "onepay_create_token_requests" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3bc1d8f332411b62144e92def4" ON "onepay_create_token_requests" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_39eeb90c6709ad3de830e85245" ON "onepay_create_token_requests" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1ec085206c9ea625ad8fc4db02" ON "chat_messages" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_914d7b70052cc92e7122057693" ON "chat_messages" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_cea6e4667d4b19760fc1b0ccb5" ON "chat_messages" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_eb51794349dc13b37c073357b9" ON "cart_item_options" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5105d1a71493956e45dd5cd1c2" ON "cart_item_options" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_c9653c25e23af780e2dc377049" ON "cart_item_options" ("deleted_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_1d43fff33753a7c91679fc2eb2" ON "cart_item_options" ("cart_item_id") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_47dc817ec8f9c29e640898f304" ON "carts" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c8af58f0a0b72b79f7a8ef7b8c" ON "carts" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_5503f8a843c33368a4fbb794f5" ON "carts" ("deleted_at") `);
    await queryRunner.query(`CREATE INDEX "IDX_8b6352f8592c91c512a89ce274" ON "carts" ("completed_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_d3e515f831ca3520b96c36d7e9" ON "carts" ("user_id", "restaurant_id", "deleted_at", "completed_at") WHERE deleted_at IS NULL AND completed_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4f0d92965a9ccd5a0ceb8b8dfb" ON "cart_items" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_95116cbad5d087f193be140c7a" ON "cart_items" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_dbf227decd2427f9bd4db5250c" ON "cart_items" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_38b72c260431ecb3e528214474" ON "cart_items" ("cart_id", "menu_item_id", "menu_section_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_93228d64ca826ab3cb6aeedcd5" ON "admin" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e44168048d11cb3223a7473557" ON "admin" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_7e91f0635e91fe4221c22ad2a7" ON "admin" ("deleted_at") `);
    await queryRunner.query(
      `CREATE INDEX "IDX_bb16ade18c0ee2059b0d38d596" ON "app_versions" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_297c0066019c2401277f476e75" ON "app_versions" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_a31e45f7acb6977a5571118da9" ON "app_versions" ("deleted_at") `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_a31e45f7acb6977a5571118da9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_297c0066019c2401277f476e75"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bb16ade18c0ee2059b0d38d596"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7e91f0635e91fe4221c22ad2a7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e44168048d11cb3223a7473557"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_93228d64ca826ab3cb6aeedcd5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_38b72c260431ecb3e528214474"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dbf227decd2427f9bd4db5250c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_95116cbad5d087f193be140c7a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4f0d92965a9ccd5a0ceb8b8dfb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d3e515f831ca3520b96c36d7e9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8b6352f8592c91c512a89ce274"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5503f8a843c33368a4fbb794f5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c8af58f0a0b72b79f7a8ef7b8c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_47dc817ec8f9c29e640898f304"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1d43fff33753a7c91679fc2eb2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c9653c25e23af780e2dc377049"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5105d1a71493956e45dd5cd1c2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_eb51794349dc13b37c073357b9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cea6e4667d4b19760fc1b0ccb5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_914d7b70052cc92e7122057693"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1ec085206c9ea625ad8fc4db02"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_39eeb90c6709ad3de830e85245"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3bc1d8f332411b62144e92def4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2518af7970bbf7cbb331c1dea5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_41b2ddc2db2359b08c9c7b488f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c546f49ee3b2c7ecf9644b592c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d463dd48773762a0df14a7c5a0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9d989fcf6aa4793ad8d30919fe"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c486903d692c953aa31b55eb30"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1e9261ebc4f646f9aeb0d62434"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_02b9c0da8bfa233dfd94e11bf1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a16e1ae67f037d93470f8b3dcb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_64ad9c317ab118f449c027cf88"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_38459db642582bd027aecf296c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cb069e13f6c65127c9d03a7f4c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ea4bd36b507c6d7fff15e149ab"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2dcda485d02e0fac6158a0f94a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1db3e61c56f2efe2801c2eddec"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cb61f258681a9b85ce04739cf0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e93de40b99d42a4e376fee2831"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_20cdc3c43d37229ed53dcb1c23"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d1b91ae8cdbcb16d1d5d908ec0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_09b0a39ef7c0b162f6a2f3c860"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f170c0e92159ed8b20349939ae"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_b942520144709ee331e18d7b44"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bfd91f86461c497971a5d27bd2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c3c58fc82087d86920f4c8a106"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_06b03b0a3688a98c801602f86a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e81f2dcea5b88695d573afc6d8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bf4c09d9591e0b4823e5f947ed"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4023fb8983b6e9afc6dd1bf44f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f87a4be76e57d041471dc95c32"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ed7dc74049eb1695cfe8605533"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_66b01417968ad057c8b347b73f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_829a1ddd92f9cc799385974bbd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_92600adff7d1d2da4aa5b24863"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_51fc747e8c26dbe526d710edbd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3807b6a5f6619228cf62cd4481"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bcb2ceb18f1cb156ac64bc89dc"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a9edb04d243fbed5770c1f7f1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_421d90c3933f487640b38bf398"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4ade87eedcd7060a58c1f82e81"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0a384ed28a3f9f9859442b70f4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4e3ef01f63307bfaa7f91b2f9b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cbc4530cd5021b052342d3a44e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f8c0228a5aa332289fb0309ece"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2493988e5e7a92d86834b19615"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ff3b0ac13d3b663ca730387535"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_1dfccf41be2f3e820dacaf8fc3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_30cb3939ac2abcc8c8ebaf3df9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6fcb53e6e5d142287659627567"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_2d4f7dc3be127de7ee4698626f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_eb6ec9ec5909e16e600360a441"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8fa903affeb684dd76f7932140"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4a3b63e2ec07e45c937a5dbc68"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9a0fd592e82969ba64d917b6a3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c041ba2187d5ec2f695daa7ad3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_af85b3e934411845173148769e"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3c988ca558690538ca8a9c931c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_d93b27402fe286a93073cef445"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_22d166a327386b29e1ef7e27e8"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_05338a97ae399a2db15ef253c9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_810bd6922e4fcab980cae85edd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a2e893c8c2915e29b4693a5dcb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c815c753f9a36488e314c4cd57"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a152afbdaa87d6b0d72c8c22d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c7cc631123c0a7c8aca098c09a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_05fbcebe65179ec1a36a4975d7"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bb5fb4ff01d1f9ef913c38c140"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6642e9bcbee8538c70dcae0d3d"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e1377fe39b40bb99bb0c2bcaf6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f03dc32e659435d58558dc4bcf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_84ab8ece3e53bf4befaa651859"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_072df1e1583f098db9cd3d07eb"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8adb65a7700c42e41b87fd671a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7c633b769fb4ea0cdec1975657"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_41e9821a5d1b51b9884182bcc0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_03b7c4b422bd6db4373bee9236"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5a5b4f6a58be6cc5297a033942"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3a8dffcf59a0df2f02ee2f16a4"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8b9b6e9bc9c08bb08106f29425"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8cf4febb6b0e685b65da6b9a1c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_c9d6021ed7fd3344f03a0a4d2c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_53e05eb0781650b76cf5536911"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f933695fb0f6fcb4e646aa5ef9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ccc0e05e4fc0c6ad80d70b0b6b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_90fc5e64ac29f4de00f704a511"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0b4667f7214f7aa3ae5ccab345"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_386d9e84282e6bc84bf27ef8d9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dc91f31bdff2b83aa566ed8eb0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_8e99465e1556dedd84b23adec1"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e7c9c60cc99ae871bdd2d2a773"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7d5025c080027789b4911d4064"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_42003fdb8497eb02f42d15bfae"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5c567422513bc84a5364bd2d49"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_cebe6e9ef8d6f83cdc093f29a0"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6002a84585068e7b46ed224fc9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_16a2caab9bdd784e68265d9f44"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3c2ee42eecaa5196c3b69ea527"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4c23fc1294c1f1d7d37d8d056f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9b09f0385ad6259f4170d17639"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3ea6ebb2fff4a021393bf95af5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_454df75392d6e23f453cb1efa3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_073999dfec9d14522f0cf58cd6"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_71a0e730d3e050468cffa42acd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3bfdfe806467b2560a3821755f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e34d25b244d759b695c81ff22f"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3c0a9d139782be53d6dbe27789"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_514f153cff3ef351d4a57e3e9a"`);
  }
}
