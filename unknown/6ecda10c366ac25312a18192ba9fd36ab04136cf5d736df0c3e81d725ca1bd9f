import { Request } from 'express';
import ms, { StringValue } from 'ms';
import { v4 as uuidv4 } from 'uuid';

import { RedisService } from '@/modules/shared/redis/redis.service';
import { UserAddressesService } from '@/modules/user-addresses/user-addresses.service';
import { User } from '@/modules/users/entities/user.entity';
import { UsersService } from '@/modules/users/users.service';
import { ACCESS_TOKEN_COOKIE_NAME, REFRESH_TOKEN_COOKIE_NAME } from '@auth/constants/auth.constants';
import { RefreshTokenDto } from '@auth/dtos/refresh-token.dto';
import { UserType } from '@auth/enums/user-type.enum';
import { UserJwtInfo, UserJwtPayload, UserJwtPayloadToVerifyPhone } from '@auth/types/jwt-payload.type';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService, JwtSignOptions } from '@nestjs/jwt';

@Injectable()
export class AuthUserService {
  private readonly logger = new Logger(AuthUserService.name);

  private acessTokenOptions: JwtSignOptions;
  private refreshTokenOptions: JwtSignOptions;
  private cookieMaxAgeAccessToken: number;
  private cookieMaxAgeRefreshToken: number;
  private readonly PHONE_VERIFICATION_TOKEN_TTL = 30 * 60; // 30 minutes in seconds

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
    private userAddressesService: UserAddressesService,
    private redisService: RedisService,
  ) {
    this.acessTokenOptions = {
      secret: this.configService.get<string>('auth.userJwtAccessSecret'),
      expiresIn: this.configService.get<string>('auth.accessTokenExpiresIn'),
    };

    this.refreshTokenOptions = {
      secret: this.configService.get<string>('auth.userJwtRefreshSecret'),
      expiresIn: this.configService.get<string>('auth.refreshTokenExpiresIn'),
    };

    this.cookieMaxAgeAccessToken = ms((this.acessTokenOptions.expiresIn as StringValue) || '1d');
    this.cookieMaxAgeRefreshToken = ms((this.refreshTokenOptions.expiresIn as StringValue) || '1d');
  }

  private getPhoneVerificationCacheKey(token: string): string {
    return `phone_verification:${token}`;
  }

  async login(user: User, response: any) {
    const defaultAddress = await this.userAddressesService.getDefaultAddress(user.id);
    const payload: UserJwtPayload = {
      sub: user.id,
      email: user.email,
      userType: UserType.USER,
      firstName: user.firstName,
      lastName: user.lastName,
      language: user.language,
      hasAddress: !!defaultAddress,
    };

    const accessToken = this.jwtService.sign(payload, this.acessTokenOptions);
    const refreshToken = this.jwtService.sign(payload, this.refreshTokenOptions);

    // Set cookies
    this.setCookies(response, accessToken, refreshToken);

    return {
      user: {
        ...user,
        userType: UserType.USER,
        hasAddress: !!defaultAddress,
        defaultAddress,
      },
      accessToken,
      refreshToken,
    };
  }

  async loginByUserId(userId: string, response: any) {
    const user = await this.usersService.findById(userId);
    return this.login(user, response);
  }

  async generateAccessTokenToVerifyPhone(phone: string, phoneCountryCode: string) {
    const token = uuidv4();
    const payload: UserJwtPayloadToVerifyPhone = {
      sub: phoneCountryCode + phone,
      phone,
      phoneCountryCode,
    };

    const cacheKey = this.getPhoneVerificationCacheKey(token);

    // Store payload in cache with TTL
    await this.redisService.setKey(cacheKey, JSON.stringify(payload), this.PHONE_VERIFICATION_TOKEN_TTL);

    this.logger.log(`Phone verification token generated for ${phoneCountryCode}${phone}`);

    return {
      phoneVerificationToken: token,
      expireAt: new Date(Date.now() + this.PHONE_VERIFICATION_TOKEN_TTL * 1000),
    };
  }

  async verifyPhoneVerificationToken(phoneVerificationToken: string): Promise<UserJwtPayloadToVerifyPhone> {
    try {
      const cacheKey = this.getPhoneVerificationCacheKey(phoneVerificationToken);
      const cachedPayload = await this.redisService.getKey(cacheKey);

      if (!cachedPayload) {
        throw new UnauthorizedException('Something went wrong, try again');
      }

      const payload: UserJwtPayloadToVerifyPhone = JSON.parse(cachedPayload);
      return payload;
    } catch (error) {
      this.logger.error(`Failed to verify phone verification token:`, error);
      throw new UnauthorizedException('Invalid phone verification token');
    }
  }

  async clearPhoneVerificationToken(phoneVerificationToken: string): Promise<void> {
    try {
      const cacheKey = this.getPhoneVerificationCacheKey(phoneVerificationToken);
      await this.redisService.deleteKey(cacheKey);
      this.logger.log(`Phone verification token cleared: ${phoneVerificationToken}`);
    } catch (error) {
      this.logger.error(`Failed to clear phone verification token:`, error);
    }
  }

  async refreshToken(request: Request, refreshTokenDto: RefreshTokenDto, response: any) {
    try {
      // Try to get refresh token from cookie first, then fall back to body
      let refreshToken = request.cookies?.refresh_token;

      if (!refreshToken) {
        refreshToken = refreshTokenDto?.refreshToken;
      }

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not found');
      }

      const decoded = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('auth.userJwtRefreshSecret'),
      });

      const user = await this.usersService.findById(decoded.sub);

      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }

      if (user.banned) {
        throw new UnauthorizedException('User is banned');
      }

      return this.login(user, response);
    } catch {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  logout(response: any) {
    // Clear cookies
    this.clearCookies(response);

    return { message: 'Logout successful' };
  }

  private setCookies(response: any, accessToken: string, refreshToken: string) {
    const cookieOptions = {
      httpOnly: this.configService.get<boolean>('auth.cookieHttpOnly'),
      secure: this.configService.get<boolean>('auth.cookieSecure'),
      sameSite: this.configService.get<string>('auth.cookieSameSite'),
    };

    response.cookie(ACCESS_TOKEN_COOKIE_NAME, accessToken, {
      maxAge: this.cookieMaxAgeAccessToken,
      ...cookieOptions,
    });
    response.cookie(REFRESH_TOKEN_COOKIE_NAME, refreshToken, {
      maxAge: this.cookieMaxAgeRefreshToken,
      ...cookieOptions,
    });
  }

  private clearCookies(response: any) {
    response.clearCookie(ACCESS_TOKEN_COOKIE_NAME);
    response.clearCookie(REFRESH_TOKEN_COOKIE_NAME);
  }

  async verifyAccessToken(token: string): Promise<UserJwtInfo | null> {
    try {
      const decoded = this.jwtService.verify(token, {
        secret: this.configService.get<string>('auth.userJwtAccessSecret'),
      }) as UserJwtPayload;

      const user = await this.usersService.findById(decoded.sub);

      if (!user || user.banned) {
        return null;
      }

      const defaultAddress = await this.userAddressesService.getDefaultAddress(user.id);

      const userInfo: UserJwtInfo = {
        id: decoded.sub,
        email: user.email,
        userType: UserType.USER,
        firstName: user.firstName,
        lastName: user.lastName,
        language: user.language,
        hasAddress: !!defaultAddress,
      };

      return userInfo;
    } catch {
      return null;
    }
  }
}
