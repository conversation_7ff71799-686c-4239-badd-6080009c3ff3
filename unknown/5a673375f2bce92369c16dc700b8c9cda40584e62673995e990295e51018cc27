import { Column, <PERSON>tity, Index, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

import { BaseEntityWithoutId } from '@/common/entities/base.entity';

import { ChatConversation } from './chat-conversation.entity';

export enum MessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  SEEN = 'seen',
}

export enum SenderType {
  USER = 'user',
  RESTAURANT = 'restaurant',
}

@Entity('chat_messages')
@Index(['conversationId', 'createdAt'], { where: 'deleted_at IS NULL' })
@Index(['userId', 'status'], { where: 'deleted_at IS NULL' })
@Index(['restaurantId', 'status'], { where: 'deleted_at IS NULL' })
export class ChatMessage extends BaseEntityWithoutId {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'conversation_id', type: 'uuid' })
  conversationId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'sender_type', type: 'enum', enum: SenderType })
  senderType: SenderType;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Column({ name: 'merchant_user_id', nullable: true, type: 'uuid' })
  merchantUserId?: string | null;

  @Column({ type: 'text' })
  content: string;

  @Column({ name: 'message_type', default: 'text', type: 'varchar' })
  messageType: string; // text, image, file, etc.

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'status', type: 'enum', enum: MessageStatus, default: MessageStatus.SENT })
  status: MessageStatus;

  @Column({ name: 'sent_at', type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  sentAt: Date;

  @Column({ name: 'delivered_at', nullable: true, type: 'timestamptz' })
  deliveredAt?: Date | null;

  @Column({ name: 'seen_at', nullable: true, type: 'timestamptz' })
  seenAt?: Date | null;

  @ManyToOne(() => ChatConversation)
  @JoinColumn({ name: 'conversation_id' })
  conversation: WrapperType<ChatConversation>;
}
