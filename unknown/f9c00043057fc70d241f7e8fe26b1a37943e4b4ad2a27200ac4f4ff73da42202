import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import { PHONE_COUNTRY_CODE_REGEX } from '../constants/phone.constant';

/**
 * Custom validator for phone country code format
 * Validates country code in format: +XX or +XXX or +XXXX
 *
 * Valid examples:
 * - +84 (Vietnam)
 * - +1 (US/Canada)
 * - +44 (UK)
 * - +420 (Czech Republic)
 * - +1234 (Some countries)
 *
 * Invalid examples:
 * - 84 (missing +)
 * - +084 (leading zero not allowed)
 * - + (no digits)
 * - +12345 (too many digits)
 */
export function IsPhoneCountryCode(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isPhoneCountryCode',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          // If value is null or undefined, it's valid (handled by @IsOptional)
          if (value == null) {
            return true;
          }

          // Must be string
          if (typeof value !== 'string') {
            return false;
          }

          return PHONE_COUNTRY_CODE_REGEX.test(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be a valid phone country code format (e.g., +84, +1, +44)`;
        },
      },
    });
  };
}
