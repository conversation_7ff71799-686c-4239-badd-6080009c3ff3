import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFieldReconciliationOptional1753695458086 implements MigrationInterface {
  name = 'UpdateFieldReconciliationOptional1753695458086';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_b1d1ee96ffadef2956fd1d94ae4"`);
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f"`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "bank_id" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "next_bank_id" DROP NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_b1d1ee96ffadef2956fd1d94ae4" FOREIGN KEY ("bank_id") REFERENCES "bank_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f" FOREIGN KEY ("next_bank_id") REFERENCES "bank_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f"`);
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_b1d1ee96ffadef2956fd1d94ae4"`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "next_bank_id" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "reconciliations" ALTER COLUMN "bank_id" SET NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f" FOREIGN KEY ("next_bank_id") REFERENCES "bank_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_b1d1ee96ffadef2956fd1d94ae4" FOREIGN KEY ("bank_id") REFERENCES "bank_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
