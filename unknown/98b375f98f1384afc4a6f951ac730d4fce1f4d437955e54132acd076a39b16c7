import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

import { PHONE_NUMBER_REGEX } from '../constants/phone.constant';

/**
 * Custom validator for phone number format
 * Validates phone number in format: 84987654321, 1234567890
 *
 * Valid examples:
 * - 84987654321 (Vietnam)
 * - 1234567890 (US/Canada)
 *
 * Invalid examples:
 * - 84 (missing digits)
 * - 12345678901 (too many digits)
 * - 1234567890a (contains letters)
 * - 1234567890-1234567890 (contains hyphens)
 */
export function IsPhoneNumber(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isPhoneNumber',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          // If value is null or undefined, it's valid (handled by @IsOptional)
          if (value == null) {
            return true;
          }

          // Must be string
          if (typeof value !== 'string') {
            return false;
          }

          // Reject 9-digit numbers starting with 0
          if (value.length === 9 && value.startsWith('0')) {
            return false;
          }

          return PHONE_NUMBER_REGEX.test(value);
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be 9-10 digits (e.g. 987654321, 0987654321) and must not start with 0 if 9 digits`;
        },
      },
    });
  };
}
