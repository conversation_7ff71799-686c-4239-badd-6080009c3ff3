import '@/types/global-types';

import { config as dotenvConfig } from 'dotenv';
import { join } from 'path';
import { DataSource, DataSourceOptions } from 'typeorm';

import { registerAs } from '@nestjs/config';

import { TypeOrmSlowQueryLogger } from './typeorm-logger';

dotenvConfig({ path: '.env' }); // Load .env file

const host = process.env.POSTGRES_HOST ?? 'localhost';
const port = parseInt(process.env.POSTGRES_PORT ?? '5432', 10);
const username = process.env.POSTGRES_USER ?? 'postgres';
const password = process.env.POSTGRES_PASSWORD ?? 'password';
const database = process.env.POSTGRES_DB ?? 'anhbeo_dev';
const logging = process.env.POSTGRES_LOGGING === 'true';
const synchronize = process.env.POSTGRES_SYNCHRONIZE === 'true';
const slowQueryThreshold = parseInt(process.env.POSTGRES_SLOW_QUERY_THRESHOLD ?? '1000', 10);

const config: DataSourceOptions = {
  type: 'postgres',
  host: host,
  port: port,
  username: username,
  password: password,
  database: database,
  entities: [join(__dirname, '..', '**', `*.entity.{ts,js}`)], // Look for entities in the project
  migrations: [join(__dirname, '..', 'database', 'migrations', '*.{ts,js}')], // Path to migration files
  synchronize, // NEVER use synchronize true in tests with migrations
  logging,
  logger: logging ? new TypeOrmSlowQueryLogger(slowQueryThreshold) : undefined,
  maxQueryExecutionTime: logging ? slowQueryThreshold : undefined, // Log queries that take longer than this threshold
  migrationsRun: process.env.NODE_ENV === 'test', // Option to auto-run migrations in test env (alternative to CLI in globalSetup)
  migrationsTableName: 'migrations', // Optional: customize migration table name
  ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false,
};

export default registerAs('typeorm', () => config);
// Export DataSource using the resolved config for CLI
export const connectionSource = new DataSource(config);
