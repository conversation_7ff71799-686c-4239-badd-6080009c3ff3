import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeleteNextBankIdReconciliations1754032444998 implements MigrationInterface {
  name = 'DeleteNextBankIdReconciliations1754032444998';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f"`);
    await queryRunner.query(`ALTER TABLE "reconciliations" DROP COLUMN "next_bank_id"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "reconciliations" ADD "next_bank_id" uuid`);
    await queryRunner.query(
      `ALTER TABLE "reconciliations" ADD CONSTRAINT "FK_ce47fe642c4a56a5c28f60fe55f" FOREIGN KEY ("next_bank_id") REFERENCES "bank_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
