import axios from 'axios';
import { Job } from 'bullmq';
import { v4 } from 'uuid';

import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface SendOtpSmsJob {
  to: string;
  code: string;
  otpExpiryMinutes: number;
}

@Processor('sms')
export class SmsProcessor extends WorkerHost {
  private readonly logger = new Logger(SmsProcessor.name);
  private readonly smsConfig: {
    apiUrl: string;
    labelId: string;
    contractId: string;
    templateId: string;
    agentId: string;
    apiUser: string;
    apiPass: string;
    username: string;
  };

  constructor(private readonly configService: ConfigService) {
    super();
    const smsConfig = this.configService.get<{
      apiUrl: string;
      labelId: string;
      contractId: string;
      templateId: string;
      agentId: string;
      apiUser: string;
      apiPass: string;
      username: string;
    }>('sms');

    if (!smsConfig) {
      this.logger.error('SMS configuration not found. SMS sending disabled.');
      return;
    }

    this.smsConfig = smsConfig;
  }

  async process(job: Job<SendOtpSmsJob>): Promise<void> {
    const { to, code, otpExpiryMinutes } = job.data;

    try {
      this.logger.log(`Processing sms job ${job.id} for ${to}`);
      await this.sendOtpSms(to, code, otpExpiryMinutes);
      this.logger.log(`Sms job ${job.id} completed successfully for ${to}`);
    } catch (error) {
      this.logger.error(`Sms job ${job.id} failed for ${to}:`, error);
      throw error; // Let BullMQ handle retries
    }
  }

  private async sendOtpSms(to: string, code: string, _otpExpiryMinutes: number): Promise<void> {
    if (!this.smsConfig) {
      this.logger.error('SMS configuration not found. Cannot send SMS.');
      // Potentially throw an error or just return depending on desired behavior
      return;
    }

    try {
      const response = await axios.post<{ RPLY: { ERROR_DESC: 'success'; name: 'send_sms_list'; ERROR: '0' } }>(
        this.smsConfig.apiUrl,
        {
          RQST: {
            name: 'send_sms_list',
            REQID: v4(),
            LABELID: this.smsConfig.labelId,
            CONTRACTTYPEID: '1',
            CONTRACTID: this.smsConfig.contractId,
            TEMPLATEID: this.smsConfig.templateId,
            PARAMS: [
              {
                NUM: '0',
                CONTENT: code,
              },
            ],
            SCHEDULETIME: '',
            MOBILELIST: to.replace('+', ''),
            ISTELCOSUB: '0',
            AGENTID: this.smsConfig.agentId,
            APIUSER: this.smsConfig.apiUser,
            APIPASS: this.smsConfig.apiPass,
            USERNAME: this.smsConfig.username,
            DATACODING: '8',
          },
        },
      );
      if (response?.data?.RPLY.ERROR !== '0') {
        this.logger.error(`Failed to send OTP sms to ${to}: ${response?.data?.RPLY?.ERROR_DESC}`);
      } else {
        this.logger.log(`OTP sms sent successfully to ${to}. Resend ID: ${response?.data?.RPLY?.ERROR_DESC}`);
      }
    } catch (error) {
      // Catch errors from template rendering or key generation too
      this.logger.error(`Error preparing or sending OTP sms to ${to}: ${error.message}`, error.stack);
      throw error; // Re-throw or handle
    }
  }
}
