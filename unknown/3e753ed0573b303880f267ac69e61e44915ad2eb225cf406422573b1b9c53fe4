import { Exclude, Expose } from 'class-transformer';
import { isNil } from 'lodash';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Order } from '@/modules/orders/entities/order.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { User } from '@/modules/users/entities/user.entity';

import { MappingRestaurantReviewTag } from './mapping-restaurant-review-tag.entity';
import { RestaurantReviewReply } from './restaurant-review-reply.entity';

@Entity('restaurant_reviews')
@Index(['restaurantId', 'createdAt'], { where: 'deleted_at IS NULL' }) // For restaurant reviews listing
@Index(['userId', 'restaurantId'], { where: 'deleted_at IS NULL' }) // For user's reviews on restaurant
@Index(['userId', 'orderId'], { unique: true, where: 'deleted_at IS NULL' })
export class RestaurantReview extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'user_id', type: 'uuid' })
  userId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'order_id', type: 'uuid' })
  orderId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'rating', type: 'integer' })
  rating: number; // 1-5 stars

  @Column({ name: 'comment', nullable: true, type: 'text' })
  comment?: string | null;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: WrapperType<User>;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant?: WrapperType<Restaurant>;

  @ManyToOne(() => Order)
  @JoinColumn({ name: 'order_id' })
  order?: WrapperType<Order>;

  @Expose()
  get reviewTags() {
    return this.mappingRestaurantReviewTags?.map((mapping) => mapping.reviewTag).filter((m) => !isNil(m));
  }

  @Exclude()
  @OneToMany(
    () => MappingRestaurantReviewTag,
    (mappingRestaurantReviewTag) => mappingRestaurantReviewTag.restaurantReview,
  )
  mappingRestaurantReviewTags?: WrapperType<MappingRestaurantReviewTag>[];

  @OneToMany(() => RestaurantReviewReply, (reply) => reply.restaurantReview)
  replies?: WrapperType<RestaurantReviewReply>[];
}
