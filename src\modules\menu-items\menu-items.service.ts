import { isNil } from 'lodash';
import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { GroupItemWithPriceDto } from '@/common/dtos/group-item-with-price.dto';
import { PositionItemDto } from '@/common/dtos/position-item.dto';
import { SectionItemWithPriceDto } from '@/common/dtos/section-item-with-price.dto';
import { Language } from '@/common/enums/language.enum';
import { NameValidationHelper } from '@/common/helpers/name-validation.helper';
import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { getCurrentTimeByTimeAndDay } from '@/helpers/time';
import { IngredientsService } from '@/modules/ingredients/ingredients.service';
import { MappingMenuItemOptionGroupMenuItemOption } from '@/modules/menu-item-option-groups/entities/mapping-menu-item-option-group-menu-item-option.entity';
import { MenuItemOptionGroup } from '@/modules/menu-item-option-groups/entities/menu-item-option-group.entity';
import { MappingMenuSectionMenuItem } from '@/modules/menu-sections/entities/mapping-menu-section-menu-item.entity';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { UserJwtInfo } from '../auth/types/jwt-payload.type';
import { MenuSection } from '../menu-sections/entities/menu-section.entity';
import { CreateMenuItemDto } from './dtos/create-menu-item.dto';
import { ListMenuItemDto } from './dtos/list-menu-item.dto';
import { UpdateMenuItemDto } from './dtos/update-menu-item.dto';
import { MappingMenuItemMenuItemOptionGroup } from './entities/mapping-menu-item-menu-item-option-group.entity';
import { MenuItem } from './entities/menu-item.entity';
import { MenuItemType } from './menu-items.constants';
import { localizeMenuItem } from './menu-items.helpers';

@Injectable()
export class MenuItemsService {
  constructor(
    @InjectRepository(MenuItem)
    private readonly menuItemRepository: Repository<MenuItem>,

    private readonly dataSource: DataSource,
    private readonly ingredientsService: IngredientsService,
    private readonly permissionMerchantUserService: PermissionMerchantUserService,
  ) {}

  async create(
    createMenuItemDto: CreateMenuItemDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
    entityManager?: EntityManager,
  ): Promise<MenuItem> {
    // Verify restaurant exists and user has access to it
    await this.permissionMerchantUserService.verifyAccessRestaurant(
      createMenuItemDto.restaurantId,
      user,
      permissionRoles,
    );

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      createMenuItemDto.restaurantId,
      createMenuItemDto.internalName,
      createMenuItemDto.publishedName,
      undefined,
      this.checkNameExists.bind(this),
    );

    let id: string = '';

    const handleCreateMenuItem = async (entityManager) => {
      // Extract ingredientIds, menuItemOptionGroupsOfItem, and menuSectionIds from DTO if present
      const {
        ingredientIds,
        menuItemOptionGroupsOfItem,
        menuItemOptionGroupsOfOption,
        menuSectionIds,
        scheduleActiveAt,
        isActive,
        ...menuItemData
      } = createMenuItemDto;

      // Create menu item with processed image URLs (store only paths)
      const menuItem = entityManager.create(MenuItem, menuItemData);

      // Handle isActive and scheduleActiveAt logic
      if (isActive) {
        menuItem.activeAt = new Date();
        menuItem.scheduleActiveAt = null;
      } else if (scheduleActiveAt) {
        menuItem.scheduleActiveAt = new Date(scheduleActiveAt);
      }

      // Handle ingredient relationships
      await this.ingredientsService.handleIngredientRelationship(menuItem, ingredientIds);

      // Save the menu item entity
      const savedMenuItem = await entityManager.save(MenuItem, menuItem);

      await this.handleRelationGroupWithOption(savedMenuItem, menuItemOptionGroupsOfOption, entityManager);

      if (menuItemData.type === MenuItemType.ITEM) {
        // Handle menu item option group relationships
        await this.handleRelationItemWithGroup(savedMenuItem, menuItemOptionGroupsOfItem, entityManager);
        // Handle menu section relationships
        await this.handleMenuSectionRelationship(savedMenuItem, menuSectionIds, entityManager);
      }

      id = savedMenuItem.id;
    };

    if (entityManager) {
      await handleCreateMenuItem(entityManager);
    } else {
      await this.dataSource.transaction(handleCreateMenuItem);
    }

    return this.findOneById(id, true, entityManager);
  }

  async findAll(
    listMenuItemDto: ListMenuItemDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Pagination<MenuItem>> {
    const { internalName, publishedName, minBasePrice, maxBasePrice, restaurantId, page, limit, type } =
      listMenuItemDto;

    const queryBuilder = this.menuItemRepository.createQueryBuilder('menuItem');

    queryBuilder
      .leftJoinAndSelect('menuItem.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
      .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup')
      .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule');

    if (restaurantId) {
      await this.permissionMerchantUserService.verifyAccessRestaurant(restaurantId, user, permissionRoles);
      queryBuilder.andWhere('menuItem.restaurantId = :restaurantId', { restaurantId });
    } else if (user.userType === UserType.MERCHANT_USER && !user.isSuperAdmin) {
      const restaurantIds = await this.permissionMerchantUserService.getPermissionList(user, permissionRoles);
      if (restaurantIds.length > 0) {
        queryBuilder.andWhere('menuItem.restaurantId IN (:...restaurantIds)', { restaurantIds });
      } else {
        queryBuilder.andWhere('1 = 0');
      }
    }

    if (type) {
      queryBuilder.andWhere('menuItem.type = :type', { type });
    }

    // Apply filters
    if (minBasePrice !== undefined) {
      queryBuilder.andWhere('menuItem.basePrice >= :minBasePrice', { minBasePrice });
    }

    if (maxBasePrice !== undefined) {
      queryBuilder.andWhere('menuItem.basePrice <= :maxBasePrice', { maxBasePrice });
    }

    if (internalName) {
      queryBuilder.andWhere('menuItem.internalName ILIKE :internalName', { internalName: `%${internalName}%` });
    }

    if (publishedName) {
      queryBuilder.andWhere('menuItem.publishedName ILIKE :publishedName', {
        publishedName: `%${publishedName}%`,
      });
    }

    // Order by most recently updated
    queryBuilder.orderBy('menuItem.updatedAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<MenuItem> {
    await this.checkPermissionById(id, user, permissionRoles);

    return this.findOneById(id, true);
  }

  async findOneById(id: string, relations?: boolean, manager?: EntityManager) {
    const queryBuilder = manager
      ? manager.createQueryBuilder(MenuItem, 'menuItem')
      : this.menuItemRepository.createQueryBuilder('menuItem');

    queryBuilder.where('menuItem.id = :id', { id }).leftJoinAndSelect('menuItem.restaurant', 'restaurant');

    if (relations) {
      queryBuilder
        .leftJoinAndSelect('menuItem.ingredients', 'ingredients')
        .leftJoinAndSelect('menuItem.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
        .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup')
        .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
        .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
        .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule');
    }

    const menuItem = await queryBuilder.getOne();

    if (!menuItem) {
      throw new NotFoundException(`Menu item not found`);
    }

    return menuItem;
  }

  async checkPermissionById(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<void> {
    const menuItem = await this.findOneById(id);
    await this.permissionMerchantUserService.verifyAccessRestaurant(menuItem.restaurantId, user, permissionRoles);
  }

  async findOneByUser(restaurantId: string, menuSectionId: string, menuItemId: string, user?: UserJwtInfo) {
    const queryBuilder = this.menuItemRepository
      .createQueryBuilder('menuItem')
      .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoin('menuSection.mappingMenus', 'mappingMenus')
      .leftJoin('mappingMenus.menu', 'menu')
      .leftJoin('menuItem.restaurant', 'restaurant')
      .addSelect('restaurant.defaultLanguage', 'restaurantDefaultLanguage')
      .leftJoin('restaurant.brand', 'brand')
      .where('menuItem.id = :itemId', { itemId: menuItemId })
      .andWhere('menuItem.restaurantId = :restaurantId', { restaurantId })
      .andWhere('mappingMenuSections.menuSectionId = :sectionId', { sectionId: menuSectionId })
      .andWhere('menuItem.type = :itemType', { itemType: MenuItemType.ITEM })
      .andWhere('brand.activeAt IS NOT NULL')
      .andWhere('menu.activeAt IS NOT NULL')
      .andWhere('menuItem.activeAt IS NOT NULL')
      .leftJoinAndSelect('menuItem.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
      .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup')
      .leftJoinAndSelect('menuItemOptionGroup.mappingMenuItemOptions', 'mappingMenuItemOptions')
      .leftJoinAndSelect('mappingMenuItemOptions.menuItemOption', 'menuItemOption');

    const menuItem = await queryBuilder.getOne();

    if (!menuItem) {
      throw new NotFoundException(`Menu item not found`);
    }

    menuItem.price = menuItem?.menuSections?.[0].itemPrice;

    if (user) localizeMenuItem(menuItem, user.language);

    return menuItem;
  }

  async update(
    id: string,
    updateMenuItemDto: UpdateMenuItemDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<MenuItem> {
    // Verify the menu item exists and user has access to it
    const menuItem = await this.findOne(id, user, permissionRoles);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      menuItem.restaurantId,
      updateMenuItemDto.internalName,
      updateMenuItemDto.publishedName,
      id,
      this.checkNameExists.bind(this),
    );

    await this.dataSource.transaction(async (entityManager) => {
      // Extract ingredient IDs, menu item option group IDs, and menu section IDs from DTO if present
      const {
        ingredientIds,
        menuItemOptionGroupsOfItem,
        menuItemOptionGroupsOfOption,
        menuSectionIds,
        scheduleActiveAt,
        isActive,
        ...menuItemData
      } = updateMenuItemDto;

      const itemWasActive = !isNil(menuItem.activeAt);
      const originalType = menuItem.type;

      // Update basic properties
      Object.assign(menuItem, menuItemData);
      delete menuItem.mappingMenuItemOptionGroups;
      delete menuItem.mappingMenuItemOptionGroupMenuItemOption;

      // Check if type is changing from 'item' to 'option'
      const isChangingFromItemToOption = originalType === MenuItemType.ITEM && menuItem.type === MenuItemType.OPTION;

      // If changing from 'item' to 'option', clear all relations with menu sections and option groups
      if (isChangingFromItemToOption) {
        // Clear menu section relationships
        await entityManager.delete(MappingMenuSectionMenuItem, { menuItemId: menuItem.id });

        // Clear option group relationships (when this item was linked to option groups)
        await entityManager.delete(MappingMenuItemMenuItemOptionGroup, { menuItemId: menuItem.id });
      }

      // Handle isActive and scheduleActiveAt logic

      if (!isNil(isActive) && itemWasActive !== isActive) {
        menuItem.activeAt = isActive ? new Date() : null;
      }

      if (!menuItem.activeAt && scheduleActiveAt !== undefined) {
        menuItem.scheduleActiveAt = scheduleActiveAt ? new Date(scheduleActiveAt) : null;
      }

      // Handle ingredient relationships if ingredientIds are provided
      await this.ingredientsService.handleIngredientRelationship(menuItem, ingredientIds);

      await this.handleRelationGroupWithOption(menuItem, menuItemOptionGroupsOfOption, entityManager);

      if (menuItemData.type === MenuItemType.ITEM) {
        // Handle menu item option group relationships
        await this.handleRelationItemWithGroup(menuItem, menuItemOptionGroupsOfItem, entityManager);
        // Handle menu section relationships
        await this.handleMenuSectionRelationship(menuItem, menuSectionIds, entityManager);
      }

      if (menuItem.type === MenuItemType.ITEM && menuItem.imageUrls?.length === 0) {
        throw new BadRequestException('Menu item image URLs are required');
      }

      // Save the updated entity
      await entityManager.save(MenuItem, menuItem);
    });

    return this.findOneById(id, true);
  }

  async softDelete(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]) {
    const menuItem = await this.findOne(id, user, permissionRoles);

    const handleDelete = async (manager: EntityManager) => {
      // Soft delete mapping relations first
      await manager.delete(MappingMenuSectionMenuItem, { menuItemId: menuItem.id });
      await manager.delete(MappingMenuItemMenuItemOptionGroup, { menuItemId: menuItem.id });

      // Then soft delete the menu item
      return manager.softRemove(menuItem);
    };

    return this.dataSource.transaction(handleDelete);
  }

  async getListByRestaurantId(restaurantId: string, menuItemIds: string[]) {
    return this.menuItemRepository.find({ where: { id: In(menuItemIds), restaurantId } });
  }

  /**
   * Helper method to handle menu item option group relationships for menu items
   * @param menuItem The menu item entity to update
   * @param menuItemOptionGroupIds Array of menu item option group IDs to link, or undefined to skip updating
   */
  private async handleRelationItemWithGroup(
    menuItem: MenuItem,
    menuItemOptionGroupIds: PositionItemDto[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemOptionGroupIds is undefined, don't update menu item option groups
    if (menuItemOptionGroupIds === undefined) {
      return;
    }
    // Clear existing mapping relationships
    if (menuItem.id) {
      await entityManager.delete(MappingMenuItemMenuItemOptionGroup, { menuItemId: menuItem.id });
    }

    // If menuItemOptionGroupIds is an empty array, we're done (all relationships cleared)
    if (menuItemOptionGroupIds.length === 0) {
      return;
    }

    // Extract IDs from PositionItemDto array
    const ids = menuItemOptionGroupIds.map((item) => item.id);

    // Find menu item option groups that match both the provided IDs and the restaurant ID
    const menuItemOptionGroups = await entityManager.find(MenuItemOptionGroup, {
      where: { id: In(ids), restaurantId: menuItem.restaurantId },
      select: ['id'],
    });

    // Check if all menu item option group IDs exist and belong to the restaurant
    if (menuItemOptionGroups.length !== ids.length) {
      const foundIds = menuItemOptionGroups.map((menuItemOptionGroup) => menuItemOptionGroup.id);
      const missingIds = ids.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following menu item option group IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Validate that the menu item has type 'item' (not 'option')
    if (menuItem.type !== MenuItemType.ITEM) {
      throw new BadRequestException(
        `Only menu items with type '${MenuItemType.ITEM}' can be linked to option groups. Menu item ID ${menuItem.id} has type '${menuItem.type}'`,
      );
    }

    // Create mapping relationships with positions
    if (menuItem.id) {
      const mappingData = menuItemOptionGroupIds.map((item) =>
        entityManager.create(MappingMenuItemMenuItemOptionGroup, {
          menuItemId: menuItem.id,
          menuItemOptionGroupId: item.id,
          position: item.position,
        }),
      );
      await entityManager.save(MappingMenuItemMenuItemOptionGroup, mappingData);
    }
  }

  /**
   * Helper method to handle option group relationships for options (add option to option groups)
   * @param menuItemOption The option entity to update
   * @param menuItemOptionGroups Array of option group IDs to link, or undefined to skip updating
   */
  private async handleRelationGroupWithOption(
    menuItemOption: MenuItem,
    menuItemOptionGroups: GroupItemWithPriceDto[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemOptionGroupIds is undefined, don't update option groups
    if (menuItemOptionGroups === undefined) {
      return;
    }

    if (!menuItemOption.id) {
      return;
    }

    // Get existing mappings where this option is linked to option groups
    const existingMappings = await entityManager.find(MappingMenuItemOptionGroupMenuItemOption, {
      where: { menuItemOptionId: menuItemOption.id },
    });

    // If menuItemOptionGroupIds is an empty array, remove all existing mappings
    if (menuItemOptionGroups.length === 0) {
      if (existingMappings.length > 0) {
        await entityManager.delete(MappingMenuItemOptionGroupMenuItemOption, {
          menuItemOptionId: menuItemOption.id,
        });
      }
      return;
    }

    const menuItemOptionGroupIds = menuItemOptionGroups.map((group) => group.groupId);

    // Verify all option group IDs exist and belong to the same restaurant
    const existingOptionGroups = await entityManager.find(MenuItemOptionGroup, {
      where: { id: In(menuItemOptionGroupIds), restaurantId: menuItemOption.restaurantId },
      select: ['id'],
    });

    if (existingOptionGroups.length !== menuItemOptionGroupIds.length) {
      const foundIds = existingOptionGroups.map((group) => group.id);
      const missingIds = menuItemOptionGroupIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `The following option group IDs do not exist or don't belong to this restaurant: ${missingIds.join(', ')}`,
      );
    }

    // Find existing option group IDs that this option is already linked to
    const existingOptionGroupIds = existingMappings.map((mapping) => mapping.menuItemOptionGroupId);

    // Find mappings to remove (existing but not in new list)
    const mappingsToRemove = existingMappings.filter(
      (mapping) => !menuItemOptionGroupIds.includes(mapping.menuItemOptionGroupId),
    );

    // Find option group IDs to add (in new list but not existing)
    const optionGroupIdsToAdd = menuItemOptionGroupIds.filter((id) => !existingOptionGroupIds.includes(id));

    // Remove mappings that are no longer needed
    if (mappingsToRemove.length > 0) {
      const optionGroupIdsToRemove = mappingsToRemove.map((mapping) => mapping.menuItemOptionGroupId);
      await entityManager.delete(MappingMenuItemOptionGroupMenuItemOption, {
        menuItemOptionId: menuItemOption.id,
        menuItemOptionGroupId: In(optionGroupIdsToRemove),
      });
    }

    // Handle existing mappings that need price updates
    const mappingsToUpdate = existingMappings.filter((existingMapping) => {
      const matchingGroup = menuItemOptionGroups.find(
        (group) => group.groupId === existingMapping.menuItemOptionGroupId,
      );
      return matchingGroup && matchingGroup.price !== existingMapping.price;
    });

    if (mappingsToUpdate.length > 0) {
      await Promise.all(
        mappingsToUpdate.map(async (mapping) => {
          const matchingGroup = menuItemOptionGroups.find((group) => group.groupId === mapping.menuItemOptionGroupId);
          if (matchingGroup) {
            await entityManager.update(
              MappingMenuItemOptionGroupMenuItemOption,
              {
                menuItemOptionGroupId: mapping.menuItemOptionGroupId,
                menuItemOptionId: mapping.menuItemOptionId,
              },
              {
                price: matchingGroup.price,
              },
            );
          }
        }),
      );
    }

    // Add new mappings with position calculated via raw query and custom price
    if (optionGroupIdsToAdd.length > 0) {
      const values = optionGroupIdsToAdd
        .map((groupId) => {
          const matchingGroup = menuItemOptionGroups.find((group) => group.groupId === groupId);
          const priceValue = !isNil(matchingGroup?.price) ? matchingGroup.price : 'NULL';
          return `('${groupId}', '${menuItemOption.id}', COALESCE((SELECT MAX(position) FROM mapping_menu_item_option_groups_menu_item_options WHERE menu_item_option_group_id = '${groupId}'), 0) + 1, ${priceValue})`;
        })
        .join(', ');

      await entityManager.query(`
        INSERT INTO mapping_menu_item_option_groups_menu_item_options (menu_item_option_group_id, menu_item_option_id, position, price)
        VALUES ${values}
      `);
    }
  }

  // ------------------- query build using in cart service -------------------
  async findOneByCartWithRelations(restaurantId: string, menuSectionId: string, menuItemId: string) {
    const queryBuilder = this.menuItemRepository
      .createQueryBuilder('menuItem')
      .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenus', 'mappingMenus')
      .leftJoinAndSelect('mappingMenus.menu', 'menu')
      .leftJoinAndSelect('menu.restaurant', 'restaurant')
      .leftJoin('restaurant.brand', 'brand')
      .where('menuItem.id = :menuItemId', { menuItemId })
      .andWhere('mappingMenuSections.menuSectionId = :menuSectionId', { menuSectionId })
      .andWhere('menuItem.restaurantId = :restaurantId', { restaurantId })
      .andWhere('menuItem.type = :itemType', { itemType: MenuItemType.ITEM })
      .andWhere('brand.activeAt IS NOT NULL')
      .andWhere('menu.activeAt IS NOT NULL')
      .andWhere('menuSection.activeAt IS NOT NULL')
      .andWhere('menuItem.activeAt IS NOT NULL')
      .leftJoinAndSelect('menuItem.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
      .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup')
      .leftJoinAndSelect('menuItemOptionGroup.mappingMenuItemOptions', 'mappingMenuItemOptions')
      .leftJoinAndSelect(
        'mappingMenuItemOptions.menuItemOption',
        'menuItemOption',
        'menuItemOption.activeAt IS NOT NULL',
      );

    const menuItem = await queryBuilder.getOne();

    if (!menuItem) {
      throw new NotFoundException(`Menu item not found`);
    }

    menuItem.price = menuItem?.menuSections?.[0].itemPrice;
    return menuItem;
  }

  async getListByRestaurantIdExcludingCartItemsWithValidation(
    restaurantId: string,
    cartItemIds: string[] | null,
    userLanguage: Language,
  ) {
    const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();

    // Step 1: Find menu item IDs that have at least one valid path to the restaurant
    const baseQueryBuilder = this.menuItemRepository
      .createQueryBuilder('menuItem')
      .select('DISTINCT menuItem.id', 'id')
      .leftJoin('menuItem.mappingMenuSections', 'mappingMenuSections')
      .leftJoin('mappingMenuSections.menuSection', 'menuSection')
      .leftJoin('menuSection.availableSchedule', 'availableSchedule')
      .leftJoin('menuSection.mappingMenus', 'mappingMenus')
      .leftJoin('mappingMenus.menu', 'menu')
      .where('menuItem.restaurantId = :restaurantId', { restaurantId })
      .andWhere('menu.activeAt IS NOT NULL')
      .andWhere('menuSection.activeAt IS NOT NULL')
      .andWhere('menuItem.activeAt IS NOT NULL')
      .andWhere('menuItem.type = :itemType', { itemType: MenuItemType.ITEM })
      // Validate menu section availability schedule
      .andWhere('availableSchedule.day = :currentDayOfWeek', { currentDayOfWeek })
      .andWhere(
        '(availableSchedule.isAllDay = true OR (availableSchedule.start IS NOT NULL AND availableSchedule.end IS NOT NULL AND :currentTime::time >= availableSchedule.start AND :currentTime::time <= availableSchedule.end))',
        { currentTime },
      );

    if (cartItemIds?.length) {
      baseQueryBuilder.andWhere('menuItem.id NOT IN (:...cartItemIds)', { cartItemIds });
    }

    const validMenuItemIds = await baseQueryBuilder
      .limit(10)
      .getRawMany()
      .then((results) => results.map((result) => result.id));

    if (validMenuItemIds.length === 0) {
      return [];
    }

    // Step 2: Query for full menu item details for valid IDs
    const menuItems = await this.menuItemRepository
      .createQueryBuilder('menuItem')
      .leftJoinAndSelect('menuItem.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
      .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup')
      .leftJoinAndSelect('menuItemOptionGroup.mappingMenuItemOptions', 'mappingMenuItemOptions')
      .leftJoinAndSelect('mappingMenuItemOptions.menuItemOption', 'menuItemOption')
      .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenus', 'mappingMenus')
      .leftJoinAndSelect('mappingMenus.menu', 'menu')
      .leftJoinAndSelect('menuItem.restaurant', 'restaurant')
      .andWhere('menu.activeAt IS NOT NULL')
      .andWhere('menuSection.activeAt IS NOT NULL')
      .where('menuItem.id IN (:...validMenuItemIds)', { validMenuItemIds })
      .getMany();

    if (menuItems.length) {
      menuItems.forEach((menuItem) => {
        localizeMenuItem(menuItem, userLanguage);
      });
    }
    return menuItems;
  }

  async getPopularItems(restaurantId: string, userLanguage?: Language) {
    const { currentTime } = getCurrentTimeByTimeAndDay();

    const queryBuilder = this.menuItemRepository.createQueryBuilder('menuItem');
    queryBuilder
      .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenus', 'mappingMenus')
      .leftJoinAndSelect('mappingMenus.menu', 'menu')
      .leftJoin('menu.restaurant', 'restaurant')
      .addSelect(['restaurant.defaultLanguage'])
      .where('menuItem.restaurantId = :restaurantId', { restaurantId })
      .andWhere('menu.activeAt IS NOT NULL')
      .andWhere('menuSection.activeAt IS NOT NULL')
      .andWhere('menuItem.activeAt IS NOT NULL')
      .andWhere('menuItem.type = :itemType', { itemType: MenuItemType.ITEM })
      .andWhere(
        '(availableSchedule.isAllDay = true OR (availableSchedule.start IS NOT NULL AND availableSchedule.end IS NOT NULL AND :currentTime::time >= availableSchedule.start AND :currentTime::time <= availableSchedule.end))',
        { currentTime },
      )
      .orderBy('menuItem.totalOrdersSold', 'DESC')
      .addOrderBy('menuItem.createdAt', 'DESC')
      .limit(5);

    const popularItems = await queryBuilder.getMany();

    return popularItems
      .map((item) => {
        const menuSection = item?.menuSections?.sort((a, b) => (a.itemPrice ?? 0) - (b.itemPrice ?? 0))[0];
        if (!menuSection) return;
        item.price = menuSection.itemPrice;
        item.menuSection = menuSection;
        if (userLanguage) {
          localizeMenuItem(item, userLanguage);
        }
        return item;
      })
      .filter((item) => !isNil(item));
  }

  async checkNameExists(
    restaurantId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      return NameValidationHelper.checkNameExists(
        this.menuItemRepository,
        'menuItem',
        restaurantId,
        'internalName',
        internalName,
        excludeId,
      );
    }

    if (publishedName) {
      return NameValidationHelper.checkNameExists(
        this.menuItemRepository,
        'menuItem',
        restaurantId,
        'publishedName',
        publishedName,
        excludeId,
      );
    }

    return false;
  }

  /**
   * Helper method to handle menu section relationships for menu items
   * @param menuItem The menu item entity to update
   * @param menuSections Array of menu section IDs to link, or undefined to skip updating
   */
  private async handleMenuSectionRelationship(
    menuItem: MenuItem,
    menuSections: SectionItemWithPriceDto[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuSectionIds is undefined, don't update menu sections
    if (menuSections === undefined) {
      return;
    }

    if (!menuItem.id) {
      return;
    }

    // Get existing mappings
    const existingMappings = await entityManager.find(MappingMenuSectionMenuItem, {
      where: { menuItemId: menuItem.id },
    });

    const existingMenuSectionIds = existingMappings.map((mapping) => mapping.menuSectionId);

    // If menuSectionIds is an empty array, remove all existing mappings
    if (menuSections.length === 0) {
      if (existingMappings.length > 0) {
        await entityManager.delete(MappingMenuSectionMenuItem, { menuItemId: menuItem.id });
      }
      return;
    }
    const menuSectionIds = menuSections.map((section) => section.sectionId);

    const existingmenuSections = await entityManager.find(MenuSection, {
      where: { id: In(menuSectionIds), restaurantId: menuItem.restaurantId },
      select: ['id'],
    });

    // Check if all menu section IDs exist and belong to the restaurant
    if (existingmenuSections.length !== menuSectionIds.length) {
      const foundIds = existingmenuSections.map((menuSection) => menuSection.id);
      const missingIds = menuSectionIds.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following menu section IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Validate that the menu item has type 'item' (not 'option')
    if (menuItem.type !== MenuItemType.ITEM) {
      throw new BadRequestException(
        `Only menu items with type '${MenuItemType.ITEM}' can be linked to menu sections. Menu item ID ${menuItem.id} has type '${menuItem.type}'`,
      );
    }

    // Find mappings to remove (existing but not in new list)
    const mappingsToRemove = existingMappings.filter((mapping) => !menuSectionIds.includes(mapping.menuSectionId));

    // Find menu section IDs to add (in new list but not existing)
    const menuSectionIdsToAdd = menuSectionIds.filter((id) => !existingMenuSectionIds.includes(id));

    // Remove mappings that are no longer needed
    if (mappingsToRemove.length > 0) {
      const menuSectionIdsToRemove = mappingsToRemove.map((mapping) => mapping.menuSectionId);
      await entityManager.delete(MappingMenuSectionMenuItem, {
        menuItemId: menuItem.id,
        menuSectionId: In(menuSectionIdsToRemove),
      });
    }

    // Handle existing mappings that need price updates
    const mappingsToUpdate = existingMappings.filter((existingMapping) => {
      const matchingGroup = menuSections.find((group) => group.sectionId === existingMapping.menuSectionId);
      return matchingGroup && matchingGroup.price !== existingMapping.price;
    });

    // Update existing mappings with new price
    if (mappingsToUpdate.length > 0) {
      await Promise.all(
        mappingsToUpdate.map(async (mapping) => {
          const matchingGroup = menuSections.find((group) => group.sectionId === mapping.menuSectionId);
          if (matchingGroup) {
            await entityManager.update(
              MappingMenuSectionMenuItem,
              {
                menuItemId: menuItem.id,
                menuSectionId: mapping.menuSectionId,
              },
              {
                price: matchingGroup.price,
              },
            );
          }
        }),
      );
    }

    // Add new mappings
    if (menuSectionIdsToAdd.length > 0) {
      // Use raw query to insert with position calculated in the query
      const values = menuSectionIdsToAdd
        .map((menuSectionId) => {
          const matchingGroup = menuSections.find((group) => group.sectionId === menuSectionId);
          const priceValue = !isNil(matchingGroup?.price) ? matchingGroup.price : 'NULL';
          return `('${menuItem.id}', '${menuSectionId}', COALESCE((SELECT MAX(position) FROM mapping_menu_sections_menu_items WHERE menu_section_id = '${menuSectionId}'), 0) + 1, ${priceValue})`;
        })
        .join(', ');

      await entityManager.query(`
        INSERT INTO mapping_menu_sections_menu_items (menu_item_id, menu_section_id, position, price)
        VALUES ${values}
      `);
    }
  }

  /**
   * Batch increment totalOrdersSold for menu items by ids
   */
  async batchIncrementTotalOrdersSold(entityManager: EntityManager, menuItemIds: string[], incrementBy = 1) {
    if (!menuItemIds.length) return;
    await entityManager
      .createQueryBuilder()
      .update(MenuItem)
      .set({ totalOrdersSold: () => `total_orders_sold + ${incrementBy}` })
      .where('id IN (:...ids)', { ids: menuItemIds })
      .execute();
  }
}
