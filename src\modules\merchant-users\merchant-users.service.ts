import * as bcrypt from 'bcrypt';
import { isBoolean } from 'lodash';
import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { generateCode } from '@/helpers/string';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateMerchantUserDto } from './dtos/create-merchant-user.dto';
import { ListMerchantUserDto } from './dtos/list-merchant-user.dto';
import { MerchantSignupDto } from './dtos/merchant-signup.dto';
import { UpdateMerchantUserDto } from './dtos/update-merchant-user.dto';
import { MerchantUserPermission } from './entities/merchant-user-permission.entity';
import { MerchantUser } from './entities/merchant-user.entity';
import { MerchantUserRole } from './enums/merchant-users-role.enum';

@Injectable()
export class MerchantUsersService {
  constructor(
    @InjectRepository(MerchantUser)
    private merchantUserRepository: Repository<MerchantUser>,
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,
    @InjectRepository(MerchantUserPermission)
    private merchantUserPermissionRepository: Repository<MerchantUserPermission>,
    private dataSource: DataSource,
  ) {}

  async create(createMerchantUserDto: CreateMerchantUserDto): Promise<MerchantUser> {
    // Check if a user with the same email already exists
    const existingUser = await this.merchantUserRepository.findOne({
      where: { email: createMerchantUserDto.email.toLowerCase() },
    });

    if (existingUser) {
      throw new BadRequestException(`Email already exists`);
    }

    // Hash the password before saving
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(createMerchantUserDto.password, saltRounds);

    // Create a new object with the hashed password
    const merchantUserData = {
      ...createMerchantUserDto,
      password: hashedPassword,
    };

    const merchantUser = this.merchantUserRepository.create(merchantUserData);
    return this.merchantUserRepository.save(merchantUser);
  }

  getMe(id: string) {
    return this.merchantUserRepository.findOne({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        isSuperAdmin: true,
        lastName: true,
        activeAt: true,
        banned: true,
      },
    });
  }

  async findAll(listMerchantUserDto: ListMerchantUserDto): Promise<Pagination<MerchantUser>> {
    const { email, firstName, lastName, banned, page, limit } = listMerchantUserDto;
    const queryBuilder = this.merchantUserRepository.createQueryBuilder('merchantUser');

    if (email) {
      queryBuilder.andWhere('merchantUser.email ILIKE :email', { email: `%${email}%` });
    }

    if (firstName) {
      queryBuilder.andWhere('merchantUser.firstName ILIKE :firstName', { firstName: `%${firstName}%` });
    }

    if (lastName) {
      queryBuilder.andWhere('merchantUser.lastName ILIKE :lastName', { lastName: `%${lastName}%` });
    }

    if (isBoolean(banned)) {
      queryBuilder.andWhere('merchantUser.banned = :banned', { banned });
    }

    queryBuilder.orderBy('merchantUser.updatedAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string): Promise<MerchantUser> {
    // Use query builder to select only specific fields from the relation
    const merchantUser = await this.merchantUserRepository
      .createQueryBuilder('merchantUser')
      .where('merchantUser.id = :id', { id })
      .getOne();

    if (!merchantUser) {
      throw new NotFoundException(`Merchant user not found`);
    }

    return merchantUser;
  }

  async update(id: string, updateMerchantUserDto: UpdateMerchantUserDto): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    // If password is being updated, hash it
    if (updateMerchantUserDto.password) {
      const saltRounds = 10;
      updateMerchantUserDto.password = await bcrypt.hash(updateMerchantUserDto.password, saltRounds);
    }

    Object.assign(merchantUser, updateMerchantUserDto);

    return this.merchantUserRepository.save(merchantUser);
  }

  async activate(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.activeAt = new Date();

    return this.merchantUserRepository.save(merchantUser);
  }

  async deactivate(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.activeAt = null;

    return this.merchantUserRepository.save(merchantUser);
  }

  async ban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = true;

    return this.merchantUserRepository.save(merchantUser);
  }

  async unban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = false;

    return this.merchantUserRepository.save(merchantUser);
  }

  findOneByEmail(email: string) {
    return this.merchantUserRepository.findOne({ where: { email } });
  }

  findById(id: string) {
    return this.merchantUserRepository.findOne({ where: { id } });
  }

  async signup(signupDto: MerchantSignupDto): Promise<{
    merchantUser: MerchantUser;
    brand: Brand;
    restaurant: Restaurant;
  }> {
    const { merchantUser: merchantUserData, brand: brandData, restaurant: restaurantData } = signupDto;

    // Check if a user with the same email already exists
    const existingUser = await this.merchantUserRepository.findOne({
      where: { email: merchantUserData.email.toLowerCase() },
    });

    if (existingUser) {
      throw new BadRequestException(`Email already exists`);
    }

    // Perform all operations in a transaction
    return await this.dataSource.transaction(async (manager) => {
      // 1. Create merchant user with inactive status
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(merchantUserData.password, saltRounds);

      const merchantUser = manager.create(MerchantUser, {
        ...merchantUserData,
        password: hashedPassword,
        activeAt: null, // Set to inactive by default
      });

      const savedMerchantUser = await manager.save(merchantUser);

      // 2. Create brand with inactive status
      const brand = manager.create(Brand, {
        name: brandData.brandName,
        businessType: brandData.businessType,
        registeredName: brandData.registeredName,
        registrationNumber: brandData.registrationNumber,
        headquarterAddress: brandData.headquarterAddress,
        headquarterWard: brandData.headquarterWard || null,
        headquarterDistrict: brandData.headquarterDistrict || null,
        headquarterProvince: brandData.headquarterProvince || null,
        headquarterLatitude: brandData.headquarterLatitude,
        headquarterLongitude: brandData.headquarterLongitude,
        headquarterLocation: {
          type: 'Point',
          coordinates: [brandData.headquarterLongitude, brandData.headquarterLatitude],
        },
        companyTaxCode: brandData.companyTaxCode,
        logoUrl: brandData.logoUrl || null,
        code: generateCode(3),
        activeAt: null, // Set to inactive by default
        includeVat: false, // Default value
      });

      const savedBrand = await manager.save(brand);

      // 3. Create restaurant with inactive status
      const restaurant = manager.create(Restaurant, {
        internalName: brandData.brandName, // Same as brand name
        publishedName: brandData.brandName, // Same as brand name
        brandId: savedBrand.id,
        avatarImg: restaurantData.avatarImg,
        backgroundImg: restaurantData.backgroundImg,
        address: restaurantData.address || null,
        ward: restaurantData.ward || null,
        district: restaurantData.district || null,
        province: restaurantData.province || null,
        phone: restaurantData.phone || null,
        latitude: restaurantData.latitude,
        longitude: restaurantData.longitude,
        location: {
          type: 'Point',
          coordinates: [restaurantData.longitude, restaurantData.latitude],
        },
        code: generateCode(3),
        activeAt: null, // Set to inactive by default
        starRated: 0,
        totalReviews: 0,
        totalOrdersSold: 0,
      });

      const savedRestaurant = await manager.save(restaurant);

      // 4. Create merchant user permission to make them owner of the brand
      const permission = manager.create(MerchantUserPermission, {
        merchantUserId: savedMerchantUser.id,
        brandId: savedBrand.id,
        role: MerchantUserRole.OWNER,
      });

      await manager.save(permission);

      return {
        merchantUser: savedMerchantUser,
        brand: savedBrand,
        restaurant: savedRestaurant,
      };
    });
  }
}
