import * as bcrypt from 'bcrypt';
import { isBoolean } from 'lodash';
import { Pagination } from 'nestjs-typeorm-paginate';
import { Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateMerchantUserDto } from './dtos/create-merchant-user.dto';
import { ListMerchantUserDto } from './dtos/list-merchant-user.dto';
import { UpdateMerchantUserDto } from './dtos/update-merchant-user.dto';
import { MerchantUser } from './entities/merchant-user.entity';

@Injectable()
export class MerchantUsersService {
  constructor(
    @InjectRepository(MerchantUser)
    private merchantUserRepository: Repository<MerchantUser>,
  ) {}

  async create(createMerchantUserDto: CreateMerchantUserDto): Promise<MerchantUser> {
    // Check if a user with the same email already exists
    const existingUser = await this.merchantUserRepository.findOne({
      where: { email: createMerchantUserDto.email.toLowerCase() },
    });

    if (existingUser) {
      throw new BadRequestException(`Email already exists`);
    }

    // Hash the password before saving
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(createMerchantUserDto.password, saltRounds);

    // Create a new object with the hashed password
    const merchantUserData = {
      ...createMerchantUserDto,
      password: hashedPassword,
    };

    const merchantUser = this.merchantUserRepository.create(merchantUserData);
    return this.merchantUserRepository.save(merchantUser);
  }

  getMe(id: string) {
    return this.merchantUserRepository.findOne({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        isSuperAdmin: true,
        lastName: true,
        activeAt: true,
        banned: true,
      },
    });
  }

  async findAll(listMerchantUserDto: ListMerchantUserDto): Promise<Pagination<MerchantUser>> {
    const { email, firstName, lastName, banned, page, limit } = listMerchantUserDto;
    const queryBuilder = this.merchantUserRepository.createQueryBuilder('merchantUser');

    if (email) {
      queryBuilder.andWhere('merchantUser.email ILIKE :email', { email: `%${email}%` });
    }

    if (firstName) {
      queryBuilder.andWhere('merchantUser.firstName ILIKE :firstName', { firstName: `%${firstName}%` });
    }

    if (lastName) {
      queryBuilder.andWhere('merchantUser.lastName ILIKE :lastName', { lastName: `%${lastName}%` });
    }

    if (isBoolean(banned)) {
      queryBuilder.andWhere('merchantUser.banned = :banned', { banned });
    }

    queryBuilder.orderBy('merchantUser.updatedAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string): Promise<MerchantUser> {
    // Use query builder to select only specific fields from the relation
    const merchantUser = await this.merchantUserRepository
      .createQueryBuilder('merchantUser')
      .where('merchantUser.id = :id', { id })
      .getOne();

    if (!merchantUser) {
      throw new NotFoundException(`Merchant user not found`);
    }

    return merchantUser;
  }

  async update(id: string, updateMerchantUserDto: UpdateMerchantUserDto): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    // If password is being updated, hash it
    if (updateMerchantUserDto.password) {
      const saltRounds = 10;
      updateMerchantUserDto.password = await bcrypt.hash(updateMerchantUserDto.password, saltRounds);
    }

    Object.assign(merchantUser, updateMerchantUserDto);

    return this.merchantUserRepository.save(merchantUser);
  }

  async activate(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.activeAt = new Date();

    return this.merchantUserRepository.save(merchantUser);
  }

  async deactivate(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.activeAt = null;

    return this.merchantUserRepository.save(merchantUser);
  }

  async ban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = true;

    return this.merchantUserRepository.save(merchantUser);
  }

  async unban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = false;

    return this.merchantUserRepository.save(merchantUser);
  }

  findOneByEmail(email: string) {
    return this.merchantUserRepository.findOne({ where: { email } });
  }

  findById(id: string) {
    return this.merchantUserRepository.findOne({ where: { id } });
  }
}
