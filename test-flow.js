const axios = require('axios');
const { faker, fakerEN } = require('@faker-js/faker');
const { sample } = require('lodash');
// Create an axios instance with cookie jar support
const api = axios.create({
  baseURL: 'http://localhost:3002',
  withCredentials: true, // Important for sending cookies with requests
});

api.interceptors.response.use((response) => {
  console.log('url:', response.request.path, 'method:', response.request.method);
  return response;
});

const post = async (url, body) => {
  const response = await api.post(url, body);
  return response.data;
};

const get = async (url, params) => {
  const response = await api.get(url, { params });
  return response.data;
};

const deleteRequest = async (url) => {
  const response = await api.delete(url);
  return response.data;
};

const put = async (url, body, params) => {
  const response = await api.put(url, body, { params });
  return response.data;
};

const itemImageUrl =
  'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/menu-items/1748006008729-e98a555a-8b57-40e9-b3b3-693b3120af81.jpeg';

const MERCHANT_USERS = '/merchant-users';
const RESTAURANT_TAGS = '/restaurant-tags';
const RESTAURENTS = '/restaurants';
const BRANDS = '/brands';
const MENUS = '/menus';
const MENU_SECTIONS = '/menu-sections';
const MENU_ITEMS = '/menu-items';
const MENU_ITEM_OPTION_GROUPS = '/menu-item-option-groups';
const INGREDIENTS = '/ingredients';
const CART_ITEMS = '/cart-items';
const ORDERS = '/orders';
const GEOFENCING = '/geofencing';

const paginationParams = { page: 1, limit: 10 };

const commonTest = async ({
  path,
  itemId = undefined,
  bodyUpdate,
  bodyCreate,
  activate = true,
  ban = false,
  params,
}) => {
  let itemCreated;
  if (bodyCreate) {
    itemCreated = await post(path, bodyCreate);
    itemId = itemCreated.id;
  }
  await get(path, params ?? paginationParams);
  await get(`${path}/${itemId}`);
  await put(`${path}/${itemId}`, bodyUpdate);
  if (activate) {
    await put(`${path}/deactivate/${itemId}`);
    await put(`${path}/activate/${itemId}`);
  }
  if (ban) {
    await put(`${path}/ban/${itemId}`);
    await put(`${path}/unban/${itemId}`);
  }

  return itemCreated;
};

// Helper function to generate random location in Hanoi inner districts
const generateHanoiLocation = () => {
  return {
    lat: fakerEN.number.float({ min: 21.0, max: 21.1, precision: 0.0001 }),
    lng: fakerEN.number.float({ min: 105.7, max: 105.9, precision: 0.0001 }),
  };
};

const createBrand = async (ownerMerchantUserId) => {
  const brandBody = { name: `Brand ${fakerEN.company.name()}`, ownerMerchantUserId };
  const brandUpdateBody = { name: `Brand ${fakerEN.company.name()}` };
  const brand = await commonTest({ path: BRANDS, bodyCreate: brandBody, bodyUpdate: brandUpdateBody });
  return brand;
};

const createRestaurant = async (brand, tags) => {
  const { lat: latitude, lng: longitude } = generateHanoiLocation();
  const internalName = `Internal ${fakerEN.company.name()}`;
  const publishedName = `Published ${fakerEN.company.name()}`;
  const restaurantBody = {
    internalName,
    publishedName,
    avatarImg:
      'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/restaurant-avatar/1748005971869-3c6bfaff-f44f-4cb5-b4ac-2acf249ccb44.png',
    backgroundImg:
      'https://anhbeodev.s3.ap-southeast-1.amazonaws.com/restaurant-banner/1748005997872-4bc39265-e5d7-441e-bfd7-a7bfd55f6d6d.jpeg',
    address: `${fakerEN.location.streetAddress()}, ${fakerEN.location.city()}`,
    ward: fakerEN.location.city(),
    district: fakerEN.location.county(),
    province: fakerEN.location.state(),
    phone: fakerEN.phone.number(),
    latitude,
    longitude,
    availableSchedule: [
      { day: 0, start: '08:00', end: '22:00', isAllDay: false },
      { day: 1, start: '08:00', end: '22:00', isAllDay: false },
      { day: 2, start: '08:00', end: '22:00', isAllDay: false },
      { day: 3, start: '08:00', end: '22:00', isAllDay: false },
      { day: 4, start: '08:00', end: '22:00', isAllDay: false },
      { day: 5, start: '08:00', end: '23:00', isAllDay: false },
      { day: 6, start: '01:00', end: '23:00', isAllDay: false },
    ],
    scheduleActiveAt: new Date().toISOString(),
    isActive: true,
  };

  const resCrBody = { ...restaurantBody, brandId: brand.id, tagIds: tags.map((tag) => tag.id) };
  const restaurantUpdateBody = {
    ...restaurantBody,
    internalName: `Updated Internal ${fakerEN.company.name()}`,
    publishedName: `Updated Published ${fakerEN.company.name()}`,
    address: `Updated ${fakerEN.location.streetAddress()}, ${fakerEN.location.city()}`,
    ward: `Updated ${fakerEN.location.city()}`,
    district: `Updated ${fakerEN.location.county()}`,
    province: `Updated ${fakerEN.location.state()}`,
    phone: fakerEN.phone.number(),
    latitude: latitude,
    longitude: longitude,
    availableSchedule: [
      { day: 0, start: '01:00', end: '23:00', isAllDay: false },
      { day: 1, start: '01:00', end: '23:00', isAllDay: false },
      { day: 2, start: '01:00', end: '23:00', isAllDay: false },
      { day: 3, start: '01:00', end: '23:00', isAllDay: false },
      { day: 4, start: '01:00', end: '23:00', isAllDay: false },
      { day: 5, start: '01:00', end: '23:00', isAllDay: false },
      { day: 6, start: '01:00', end: '23:00', isAllDay: false },
    ],
  };
  const restaurant = await commonTest({ path: RESTAURENTS, bodyCreate: resCrBody, bodyUpdate: restaurantUpdateBody });

  return restaurant;
};

const runTestAllCase = async (restaurant) => {
  // --------------- Create and test Geofencing Areas ----------------------
  console.log('Testing Geofencing Areas...');

  // Circle geofencing
  const circleGeofencing = {
    name: `Circle Zone ${fakerEN.company.name()}`,
    description: `Delivery zone within ${fakerEN.number.int({ min: 1, max: 5 })}km radius`,
    shippingFee: fakerEN.number.int({ min: 5, max: 20 }),
    geometryData: {
      snapshot: {
        center: generateHanoiLocation(),
        radius: fakerEN.number.int({ min: 1000, max: 5000 }), // meters
      },
      type: 'circle',
      fillColor: '#FF6B6B',
    },
  };

  // Rectangle geofencing
  const rectangleGeofencing = {
    name: `Rectangle Zone ${fakerEN.company.name()}`,
    description: `Rectangular delivery area`,
    shippingFee: fakerEN.number.int({ min: 8, max: 25 }),
    geometryData: {
      snapshot: {
        bounds: (() => {
          const center = generateHanoiLocation();
          const offset = 0.01;

          return {
            south: center.lat - offset,
            west: center.lng - offset,
            north: center.lat + offset,
            east: center.lng + offset,
          };
        })(),
      },
      type: 'rectangle',
      fillColor: '#4ECDC4',
    },
  };

  // Polygon geofencing
  const polygonGeofencing = {
    name: `Polygon Zone ${fakerEN.company.name()}`,
    description: `Custom polygon delivery area`,
    shippingFee: fakerEN.number.int({ min: 10, max: 30 }),
    geometryData: {
      snapshot: {
        path: (() => {
          const center = generateHanoiLocation();
          return [
            { lat: center.lat, lng: center.lng },
            { lat: center.lat + 0.005, lng: center.lng + 0.005 },
            { lat: center.lat + 0.01, lng: center.lng },
            { lat: center.lat + 0.005, lng: center.lng - 0.005 },
            { lat: center.lat, lng: center.lng },
          ];
        })(),
      },
      type: 'polygon',
      fillColor: '#45B7D1',
    },
  };

  // Create all three types of geofencing areas
  await post(GEOFENCING, {
    restaurantId: restaurant.id,
    geofencingAreas: [circleGeofencing, rectangleGeofencing, polygonGeofencing],
  });

  // Get geofencing areas for the restaurant
  await get(`${GEOFENCING}/restaurant/${restaurant.id}`);

  // --------------- Create and test Ingredient ----------------------
  console.log('Testing Ingredients...');
  const ingCrBody = {
    internalName: `Ingredient ${fakerEN.company.name()}`,
    publishedName: `Published Ingredient ${fakerEN.company.name()}`,
    restaurantId: restaurant.id,
  };

  const ingUdBody = {
    internalName: `Updated Ingredient ${fakerEN.company.name()}`,
    publishedName: `Updated Published Ingredient ${fakerEN.company.name()}`,
  };

  const ingredients = await commonTest({
    path: INGREDIENTS,
    bodyCreate: ingCrBody,
    bodyUpdate: ingUdBody,
    activate: false,
  });

  // --------------- Create and test Menu Item Option Group 1 ---------------

  console.log('Testing Menu Item Option Groups 1...');
  const grCrBody = {
    internalName: `Option Group ${fakerEN.company.name()}`,
    publishedName: `Published Option Group ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Option Group ${fakerEN.company.name()}`,
    publishedNameVi: `Nhóm Option Đã Xuất Bản ${fakerEN.company.name()}`,
    rule: 'option',
    type: 'item_customization',
    restaurantId: restaurant.id,
  };

  const grUdBody = {
    internalName: `Updated Option Group ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option Group ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Option Group ${fakerEN.company.name()}`,
    publishedNameVi: `Nhóm Option Đã Cập Nhật ${fakerEN.company.name()}`,
    rule: 'option_max',
    type: 'item_customization',
    maxAmount: 3,
  };

  const groups = await commonTest({
    path: MENU_ITEM_OPTION_GROUPS,
    bodyCreate: grCrBody,
    bodyUpdate: grUdBody,
    activate: false,
  });

  // --------------- Create and test Menu Section 1 ---------------
  console.log('Testing Menu Sections 1...');

  const availableSchedule = [
    { day: 0, start: '00:00', end: '23:59' },
    { day: 1, start: '00:00', end: '23:59' },
    { day: 2, start: '00:00', end: '23:59' },
    { day: 3, start: '00:00', end: '23:59' },
    { day: 4, start: '00:00', end: '23:59' },
    { day: 5, start: '00:00', end: '23:59' },
    { day: 6, start: '00:00', end: '23:59' },
  ];

  const secCrBody = {
    internalName: `Menu Section ${fakerEN.company.name()}`,
    publishedName: `Published Menu Section ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Menu Section ${fakerEN.company.name()}`,
    publishedNameVi: `Menu Đã Xuất Bản ${fakerEN.company.name()}`,
    viewType: 'grid',
    isActive: true,
    availableSchedule: [
      { day: 1, start: '10:00', end: '14:00' },
      { day: 2, start: '10:00', end: '14:00' },
    ],
    restaurantId: restaurant.id,
  };

  const secUdBody = {
    internalName: `Updated Menu Section ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Section ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Menu Section ${fakerEN.company.name()}`,
    publishedNameVi: `Menu Đã Cập Nhật ${fakerEN.company.name()}`,
    availableSchedule,
  };

  const sections = await commonTest({
    path: MENU_SECTIONS,
    bodyCreate: secCrBody,
    bodyUpdate: secUdBody,
    activate: false,
  });

  // --------------- Create and test Menu Item Option ---------------

  console.log('Testing Menu Item Options no gradient...');
  const optionCrBody = {
    internalName: `Option ${fakerEN.company.name()}`,
    publishedName: `Published Option ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Option ${fakerEN.company.name()}`,
    publishedNameVi: `Option Đã Xuất Bản ${fakerEN.company.name()}`,
    description: `Description for option ${fakerEN.company.name()}`,
    descriptionEn: `English description for option ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả tùy chọn ${fakerEN.company.name()}`,
    basePrice: 3,
    isActive: true,
    type: 'option',
    restaurantId: restaurant.id,
    imageUrls: [itemImageUrl],
    menuItemOptionGroupsOfOption: [{ groupId: groups.id, price: 10 }],
  };

  const optionUdBody = {
    internalName: `Updated Option ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Option ${fakerEN.company.name()}`,
    publishedNameVi: `Option Đã Cập Nhật ${fakerEN.company.name()}`,
    description: `Updated description for option ${fakerEN.company.name()}`,
    descriptionEn: `Updated English description for option ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả tùy chọn đã cập nhật ${fakerEN.company.name()}`,
    basePrice: 4,
    ingredientIds: [ingredients.id],
    imageUrls: [itemImageUrl],
    type: 'option',
  };

  const options = await commonTest({
    path: MENU_ITEMS,
    bodyCreate: optionCrBody,
    bodyUpdate: optionUdBody,
    activate: false,
  });

  console.log('Testing Menu Item Options with gradient...');
  const optionCrBody2 = {
    internalName: `Option ${fakerEN.company.name()}`,
    publishedName: `Published Option ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Option ${fakerEN.company.name()}`,
    publishedNameVi: `Option Đã Xuất Bản ${fakerEN.company.name()}`,
    description: `Description for option ${fakerEN.company.name()}`,
    descriptionEn: `English description for option ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả tùy chọn ${fakerEN.company.name()}`,
    basePrice: 3,
    isActive: true,
    ingredientIds: [ingredients.id],
    restaurantId: restaurant.id,
    type: 'option',
    imageUrls: [itemImageUrl],
  };

  const optionUdBody2 = {
    internalName: `Updated Option ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Option ${fakerEN.company.name()}`,
    publishedNameVi: `Option Đã Cập Nhật ${fakerEN.company.name()}`,
    description: `Updated description for option ${fakerEN.company.name()}`,
    descriptionEn: `Updated English description for option ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả tùy chọn đã cập nhật ${fakerEN.company.name()}`,
    basePrice: 4,
    ingredientIds: [],
    type: 'option',
    imageUrls: [itemImageUrl],
  };

  const options2 = await commonTest({
    path: MENU_ITEMS,
    bodyCreate: optionCrBody2,
    bodyUpdate: optionUdBody2,
    activate: false,
  });

  // --------------- Create and test Menu Item Option Group 2 ---------------

  console.log('Testing Menu Item Option Groups 2...');
  const grCrBody2 = {
    internalName: `Option Group ${fakerEN.company.name()}`,
    publishedName: `Published Option Group ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Option Group ${fakerEN.company.name()}`,
    publishedNameVi: `Nhóm Tùy Chọn Đã Xuất Bản ${fakerEN.company.name()}`,
    rule: 'option',
    type: 'add_on_item',
    restaurantId: restaurant.id,
    menuItemOptionIds: [{ id: options.id, position: 0, price: 1 }],
  };

  const grUdBody2 = {
    internalName: `Updated Option Group ${fakerEN.company.name()}`,
    publishedName: `Updated Published Option Group ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Option Group ${fakerEN.company.name()}`,
    publishedNameVi: `Nhóm Tùy Chọn Đã Cập Nhật ${fakerEN.company.name()}`,
    rule: 'mandatory_fixed',
    type: 'add_on_item',
    fixedAmount: 2,
    menuItemOptionIds: [],
  };

  const groups2 = await commonTest({
    path: MENU_ITEM_OPTION_GROUPS,
    bodyCreate: grCrBody2,
    bodyUpdate: grUdBody2,
    activate: false,
  });

  // --------------- Create and test Menu Item ---------------
  // Create and test Menu Item
  console.log('Testing Menu Items without gradient...');
  const itemCrBody = {
    internalName: `Menu Item ${fakerEN.company.name()}`,
    publishedName: `Published Menu Item ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Menu Item ${fakerEN.company.name()}`,
    publishedNameVi: `Món Ăn Đã Xuất Bản ${fakerEN.company.name()}`,
    description: `Description for menu item ${fakerEN.company.name()}`,
    descriptionEn: `English description for menu item ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả món ăn ${fakerEN.company.name()}`,
    basePrice: 10,
    restaurantId: restaurant.id,
    type: 'item',
    isActive: true,
    imageUrls: [itemImageUrl],
  };

  const itemUdBody = {
    internalName: `Updated Menu Item ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Item ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Menu Item ${fakerEN.company.name()}`,
    publishedNameVi: `Món Ăn Đã Cập Nhật ${fakerEN.company.name()}`,
    description: `Updated description for menu item ${fakerEN.company.name()}`,
    descriptionEn: `Updated English description for menu item ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả món ăn đã cập nhật ${fakerEN.company.name()}`,
    basePrice: 11,
    type: 'item',
    ingredientIds: [ingredients.id],
    menuItemOptionGroupsOfItem: [{ id: groups.id, position: 0 }],
    imageUrls: [itemImageUrl],
  };

  const items = await commonTest({
    path: MENU_ITEMS,
    bodyCreate: itemCrBody,
    bodyUpdate: itemUdBody,
    activate: false,
  });

  // Create and test Menu Item
  console.log('Testing Menu Items with gradient...');
  const itemCrBody2 = {
    internalName: `Menu Item ${fakerEN.company.name()}`,
    publishedName: `Published Menu Item ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Menu Item ${fakerEN.company.name()}`,
    publishedNameVi: `Món Ăn Đã Xuất Bản ${fakerEN.company.name()}`,
    description: `Description for menu item ${fakerEN.company.name()}`,
    descriptionEn: `English description for menu item ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả món ăn ${fakerEN.company.name()}`,
    basePrice: 10,
    isActive: true,
    type: 'item',
    ingredientIds: [ingredients.id],
    menuItemOptionGroupsOfItem: [{ id: groups.id, position: 0 }],
    restaurantId: restaurant.id,
    imageUrls: [itemImageUrl],
  };

  const itemUdBody2 = {
    internalName: `Updated Menu Item ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Item ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Menu Item ${fakerEN.company.name()}`,
    publishedNameVi: `Món Ăn Đã Cập Nhật ${fakerEN.company.name()}`,
    description: `Updated description for menu item ${fakerEN.company.name()}`,
    descriptionEn: `Updated English description for menu item ${fakerEN.company.name()}`,
    descriptionVi: `Mô tả món ăn đã cập nhật ${fakerEN.company.name()}`,
    basePrice: 11,
    ingredientIds: [],
    type: 'item',
    menuItemOptionGroupsOfItem: [],
    imageUrls: [itemImageUrl],
  };

  const items2 = await commonTest({
    path: MENU_ITEMS,
    bodyCreate: itemCrBody2,
    bodyUpdate: itemUdBody2,
    activate: false,
  });

  delete itemCrBody2.menuItemOptionGroupsOfItem;

  itemUdBody2.internalName = `Updated Menu Item ${fakerEN.company.name()}`;
  itemUdBody2.publishedName = `Updated Published Menu Item ${fakerEN.company.name()}`;
  itemUdBody2.menuItemOptionGroupsOfOption = [{ groupId: groups.id, price: 28 }];
  delete itemUdBody2.menuItemOptionGroupsOfItem;

  const items3 = await commonTest({
    path: MENU_ITEMS,
    bodyCreate: itemCrBody2,
    bodyUpdate: { ...itemUdBody2, type: 'option' },
    activate: false,
  });
  // --------------- Create and test Menu Section 2 ---------------
  console.log('Testing Menu Sections 2...');
  const secCrBody2 = {
    internalName: `Menu Section ${fakerEN.company.name()}`,
    publishedName: `Published Menu Section ${fakerEN.company.name()}`,
    publishedNameEn: `English Published Menu Section ${fakerEN.company.name()}`,
    publishedNameVi: `Phần Menu Đã Xuất Bản ${fakerEN.company.name()}`,
    viewType: 'spotlight',
    isActive: true,
    availableSchedule: [
      { day: 1, start: '10:00', end: '14:00' },
      { day: 2, start: '10:00', end: '14:00' },
    ],
    menuItemIds: [],
    restaurantId: restaurant.id,
  };

  const secUdBody2 = {
    internalName: `Updated Menu Section ${fakerEN.company.name()}`,
    publishedName: `Updated Published Menu Section ${fakerEN.company.name()}`,
    publishedNameEn: `Updated English Published Menu Section ${fakerEN.company.name()}`,
    publishedNameVi: `Phần Menu Đã Cập Nhật ${fakerEN.company.name()}`,
    availableSchedule,
    menuItemIds: [
      { id: items.id, position: 0, price: 12 },
      { id: items2.id, position: 1, price: null },
    ],
  };

  const sections2 = await commonTest({
    path: MENU_SECTIONS,
    bodyCreate: secCrBody2,
    bodyUpdate: secUdBody2,
    activate: false,
  });

  // --------------- Create and test Menu ---------------
  console.log('Testing Menus 1...');
  const menuCrBody = {
    name: `Menu ${fakerEN.company.name()}`,
    restaurantId: restaurant.id,
  };

  const menuUdBody = {
    name: `Updated Menu ${fakerEN.company.name()}`,
    menuSectionIds: [
      { id: sections.id, position: 0 },
      { id: sections2.id, position: 1 },
    ],
  };

  const menu = await commonTest({ path: MENUS, bodyCreate: menuCrBody, bodyUpdate: menuUdBody });

  console.log('Testing Menus 2...');
  const menuCrBody2 = {
    name: `Menu ${fakerEN.company.name()}`,
    restaurantId: restaurant.id,
    menuSectionIds: [
      { id: sections.id, position: 0 },
      { id: sections2.id, position: 1 },
    ],
  };

  const menuUdBody2 = {
    name: `Updated Menu ${fakerEN.company.name()}`,
    menuSectionIds: [
      { id: sections.id, position: 0 },
      { id: sections2.id, position: 1 },
    ],
  };

  const menu2 = await commonTest({ path: MENUS, bodyCreate: menuCrBody2, bodyUpdate: menuUdBody2 });

  return {
    menu,
    menu2,
    items,
    items2,
    sections,
    sections2,
    options,
    options2,
    groups,
    groups2,
    ingredients,
  };
};

const main = async () => {
  try {
    const defaultPassword = '123456';
    const adminLogin = { email: '<EMAIL>', password: 'ChangeMe123!' };
    const emailMerchant = `${faker.internet.username()}@gmail.com`;
    const merchantUserBody = {
      email: emailMerchant,
      firstName: fakerEN.person.firstName(),
      lastName: fakerEN.person.lastName(),
      password: defaultPassword,
    };

    const merchantUserAdminBody = {
      email: '<EMAIL>',
      firstName: fakerEN.person.firstName(),
      lastName: fakerEN.person.lastName(),
      password: defaultPassword,
      isSuperAdmin: true,
    };

    const res = await post('/admin/auth/login', adminLogin);
    api.defaults.headers.Cookie = `access_token=${res.accessToken}`;
    console.log('---------- Login as admin ----------');

    const merchantUser = await post(MERCHANT_USERS, merchantUserBody);
    try {
      const res = await post(MERCHANT_USERS, merchantUserAdminBody);
      await put(`${MERCHANT_USERS}/activate/${res.id}`);
    } catch {}
    console.log('Email', JSON.stringify({ email: merchantUser.email, password: defaultPassword }));

    const merchantUpdateBody = {
      firstName: fakerEN.person.firstName(),
      lastName: fakerEN.person.lastName(),
      password: defaultPassword,
    };
    await commonTest({ path: MERCHANT_USERS, itemId: merchantUser.id, bodyUpdate: merchantUpdateBody, ban: true });

    // GET
    // /restaurant-tags
    const listTags = await get('/restaurant-tags?page=1&limit=50');

    const tags = [sample(listTags.items), sample(listTags.items), sample(listTags.items)];

    // const tagCrBody = { name: `Tag ${fakerEN.company.name()}` };
    // const tagUdBody = { name: `Updated Tag ${fakerEN.company.name()}` };

    // const tag = await commonTest({
    //   path: RESTAURANT_TAGS,
    //   bodyCreate: tagCrBody,
    //   bodyUpdate: tagUdBody,
    //   activate: false,
    // });

    // await runTestAllCase(merchantAccount, tag);

    const brand = await createBrand(merchantUser.id);

    const resLoginAdmin = await post('/auth/merchant-user/login', {
      email: merchantUserAdminBody.email,
      password: defaultPassword,
    });
    api.defaults.headers.Cookie = `access_token=${resLoginAdmin.accessToken}`;
    console.log('merchant accessToken: ', resLoginAdmin.accessToken);

    console.log('---------- Login as merchant admin ----------');

    const restaurant = await createRestaurant(brand, tags);

    const resLogin = await post('/auth/merchant-user/login', { email: emailMerchant, password: defaultPassword });
    api.defaults.headers.Cookie = `access_token=${resLogin.accessToken}`;
    console.log('merchant accessToken: ', resLogin.accessToken);

    console.log('---------- Login as merchant ----------');

    const { menu, menu2, items, items2, sections, sections2, options, options2, groups, groups2, ingredients } =
      await runTestAllCase(restaurant);

    const isNotiStaff = undefined;
    const token =
      'dDyiYEInPsJ8NX00UAZIj3:APA91bFDm4XB7GRui85QeF_YBzYSd1peyngplLuDzbN25HPQ3J9IVgzirjWqmPlEhhhSfNk3t-iFMON3Bq2Jp2MxbGaZcrMsL0muEsHlgJmbYDJpMONqbm8';

    if (isNotiStaff) {
      // Test staff fcm token
      const resLoginStaff = await post('/auth/merchant-staff/login', {
        username: restaurant.merchantStaff.username,
        password: defaultPassword,
      });
      api.defaults.headers.Cookie = `access_token=${resLoginStaff.accessToken}`;
      console.log('staff accessToken: ', resLoginStaff.accessToken);
      console.log('---------- Login as staff ----------');
      await post('/staff/fcm-tokens', {
        token: token,
        deviceId: 'device-123',
        platform: 'web',
      });
    }
    // return;
    // Test user cart functionality
    const email = '<EMAIL>';
    let resVerifyEmailOtp;
    try {
      resVerifyEmailOtp = await post('/auth/user-fake/login-fake', {
        email,
      });
    } catch {
      // fake flow register user
      const phoneBody = {
        phone: '987654321',
        phoneCountryCode: '+84',
      };
      const resSendOtp = await post('/auth/user/send-phone-otp', phoneBody);
      const { phoneVerificationToken } = await post('/auth/user/verify-phone-otp', {
        ...phoneBody,
        otp: resSendOtp.data.code,
      });
      const resEmailOtp = await post('/auth/user/send-email-otp', { email, phoneVerificationToken });
      resVerifyEmailOtp = await post('/auth/user/verify-email-otp', {
        email,
        otp: resEmailOtp.data.code,
        phoneVerificationToken,
      });
    }

    console.log('user accessToken: ', resVerifyEmailOtp.accessToken);

    api.defaults.headers.Cookie = `access_token=${resVerifyEmailOtp.accessToken}`;

    await post('/auth/user/update-profile', { firstName: 'John', lastName: 'Doe' });
    const { lat: latitude, lng: longitude } = generateHanoiLocation();
    await post('/auth/user/temporary', {
      latitude,
      longitude,
      fullAddress: '123 Main St, New York, NY 10001, USA',
      addressType: 'string',
      addressLabel: 'Home',
      placeId: 'string',
    });

    console.log('---------- Login as user ----------');

    if (isNotiStaff === false) {
      // Test user fcm token
      await post('/user/fcm-tokens', {
        token: token,
        deviceId: 'device-123',
        platform: 'web',
      });
    }

    const paymentMethod = true ? 'cash' : 'credit_card';
    const numberOfOrders = 1;

    let paymentCards;
    if (paymentMethod === 'credit_card') {
      paymentCards = await get(`/user/payment-cards`);
      if (!paymentCards?.length) {
        throw new Error('No payment cards found');
      }
    }
    const baseCrOrderBody = {
      restaurantId: restaurant.id,
      menuItemId: items.id,
      menuSectionId: sections2.id,
      groupOptions: [
        {
          optionGroupId: groups.id,
          options: [{ id: options.id, amount: 1 }],
        },
        // {
        //   optionGroupId: groups.id,
        //   options: [{ id: options2.id, amount: 2 }],
        // },
      ],
    };
    for (let i = 0; i < numberOfOrders; i++) {
      const cart = await post(`/user/carts/add`, {
        ...baseCrOrderBody,
        amount: 2,
      });
      await post(`/user/carts/add`, {
        restaurantId: restaurant.id,
        menuItemId: items.id,
        menuSectionId: sections2.id,
        amount: 3,
        groupOptions: [],
      });
      await post(`/user/carts/add`, {
        ...baseCrOrderBody,
        amount: 4,
      });

      await post(`/user/orders`, {
        cartId: cart.id,
        paymentMethod,
        paymentCardId: paymentCards?.[0].id,
      });
    }

    console.log('All restaurant management module tests completed successfully!');
  } catch (e) {
    console.error(e.config?.method, e?.response?.data ?? e.message ?? 'Unknown error');
  }
};

const numberOfTest = 1;

async function multipleTest() {
  for (let i = 0; i < numberOfTest; i++) {
    await main();
  }
}

multipleTest();
