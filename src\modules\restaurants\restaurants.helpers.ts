import { Language } from '@/common/enums/language.enum';
import { LocalizationHelper } from '@/common/helpers/localization.helper';

import type { Restaurant } from './entities/restaurant.entity';
import type { MenuItemOptionGroup } from '../menu-item-option-groups/entities/menu-item-option-group.entity';
import type { MenuItem } from '../menu-items/entities/menu-item.entity';
import type { MenuSection } from '../menu-sections/entities/menu-section.entity';

export const localizeRestaurant = (restaurant: Restaurant, userLanguage: Language) => {
  const restaurantLanguage = restaurant.defaultLanguage;
  LocalizationHelper.localizeMenuItemEntity(restaurant, userLanguage, restaurantLanguage);

  if (!restaurant.menu?.mappingMenuSections) return;

  for (const { menuSection } of restaurant.menu.mappingMenuSections) {
    if (!menuSection) continue;
    localizeMenuSection(menuSection, userLanguage, restaurantLanguage);
  }
};

const localizeMenuSection = (menuSection: MenuSection, userLanguage: Language, restaurantLanguage: Language) => {
  LocalizationHelper.localizeMenuItemEntity(menuSection, userLanguage, restaurantLanguage);

  if (!menuSection.mappingMenuItems) return;

  for (const { menuItem } of menuSection.mappingMenuItems) {
    if (!menuItem) continue;
    localizeMenuItem(menuItem, userLanguage, restaurantLanguage);
  }
};

const localizeMenuItem = (menuItem: MenuItem, userLanguage: Language, restaurantLanguage: Language) => {
  LocalizationHelper.localizeMenuItemEntity(menuItem, userLanguage, restaurantLanguage);

  if (!menuItem.mappingMenuItemOptionGroups) return;

  for (const { menuItemOptionGroup } of menuItem.mappingMenuItemOptionGroups) {
    if (!menuItemOptionGroup) continue;
    localizeMenuItemOptionGroup(menuItemOptionGroup, userLanguage, restaurantLanguage);
  }
};

const localizeMenuItemOptionGroup = (
  menuItemOptionGroup: MenuItemOptionGroup,
  userLanguage: Language,
  restaurantLanguage: Language,
) => {
  LocalizationHelper.localizeMenuItemEntity(menuItemOptionGroup, userLanguage, restaurantLanguage);

  if (!menuItemOptionGroup.mappingMenuItemOptions) return;

  for (const { menuItemOption } of menuItemOptionGroup.mappingMenuItemOptions) {
    if (!menuItemOption) continue;
    LocalizationHelper.localizeMenuItemEntity(menuItemOption, userLanguage, restaurantLanguage);
  }
};
