import { isNil } from 'lodash';
import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { PositionItemWithPricePositiveDto } from '@/common/dtos/position-item-with-price.dto';
import { NameValidationHelper } from '@/common/helpers/name-validation.helper';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { MenuItemType } from '@/modules/menu-items/menu-items.constants';
import { MappingMenuMenuSection } from '@/modules/menus/entities/mapping-menu-menu-section.entity';
import { Menu } from '@/modules/menus/entities/menu.entity';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateMenuSectionDto, ScheduleItem } from './dtos/create-menu-section.dto';
import { ListMenuSectionDto, MenuSectionSortBy } from './dtos/list-menu-section.dto';
import { UpdateMenuSectionDto } from './dtos/update-menu-section.dto';
import { MappingMenuSectionMenuItem } from './entities/mapping-menu-section-menu-item.entity';
import { MenuSectionAvailableSchedule } from './entities/menu-section-available-schedule.entity';
import { MenuSection } from './entities/menu-section.entity';

@Injectable()
export class MenuSectionsService {
  constructor(
    @InjectRepository(MenuSection)
    private menuSectionRepository: Repository<MenuSection>,
    private permissionMerchantUserService: PermissionMerchantUserService,
    private dataSource: DataSource,
  ) {}

  async create(
    createMenuSectionDto: CreateMenuSectionDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<MenuSection> {
    await this.permissionMerchantUserService.verifyAccessRestaurant(
      createMenuSectionDto.restaurantId,
      user,
      permissionRoles,
    );

    // Validate unique names using helper

    await NameValidationHelper.validateUniqueNames(
      createMenuSectionDto.restaurantId,
      createMenuSectionDto.internalName,
      createMenuSectionDto.publishedName,
      undefined,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemIds, menuIds and availableSchedule from DTO if present
    const { menuItemIds, menuIds, isActive, scheduleActiveAt, availableSchedule, ...menuSectionData } =
      createMenuSectionDto;

    let id: string = '';
    await this.dataSource.transaction(async (entityManager) => {
      // Create menu section
      const menuSection = entityManager.create(MenuSection, menuSectionData);
      if (isActive) {
        menuSection.activeAt = new Date();
        menuSection.scheduleActiveAt = null;
      } else if (scheduleActiveAt) {
        menuSection.scheduleActiveAt = new Date(scheduleActiveAt);
      }

      // Save the menu section
      const savedMenuSection = await entityManager.save(menuSection);

      // Handle available schedule if provided
      await this.handleAvailableSchedule(savedMenuSection, availableSchedule, entityManager);

      // Handle menu item relationships if menuItemIds are provided
      await this.handleMenuItemRelationship(savedMenuSection, menuItemIds, entityManager);

      // Handle menu relationships if menuIds are provided
      await this.handleMenuRelationship(savedMenuSection, menuIds, entityManager);

      id = savedMenuSection.id;
    });

    return this.findOneById(id, true);
  }

  async findAll(
    listMenuSectionDto: ListMenuSectionDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Pagination<MenuSection>> {
    const { internalName, publishedName, restaurantId, page, limit, isActive, sortBy, sort } = listMenuSectionDto;

    const queryBuilder = this.menuSectionRepository.createQueryBuilder('menuSection');

    queryBuilder.leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule');

    if (restaurantId) {
      await this.permissionMerchantUserService.verifyAccessRestaurant(restaurantId, user, permissionRoles);
      queryBuilder.andWhere('menuSection.restaurantId = :restaurantId', { restaurantId });
    } else if (user.userType === UserType.MERCHANT_USER && !user.isSuperAdmin) {
      const restaurantIds = await this.permissionMerchantUserService.getPermissionList(user, permissionRoles);
      if (restaurantIds.length > 0) {
        queryBuilder.andWhere('menuSection.restaurantId IN (:...restaurantIds)', { restaurantIds });
      } else {
        queryBuilder.andWhere('1 = 0');
      }
    }

    // Apply filters
    if (internalName) {
      queryBuilder.andWhere('menuSection.internalName ILIKE :internalName', { internalName: `%${internalName}%` });
    }

    if (publishedName) {
      queryBuilder.andWhere('menuSection.publishedName ILIKE :publishedName', {
        publishedName: `%${publishedName}%`,
      });
    }

    if (!isNil(isActive)) {
      queryBuilder.andWhere(`menuSection.activeAt ${isActive ? 'IS NOT NULL' : 'IS NULL'}`);
    }

    // Add count of items as subquery
    queryBuilder.addSelect(
      (subQuery) =>
        subQuery
          .select('COUNT(*)')
          .from(MappingMenuSectionMenuItem, 'mappingMenuItem')
          .where('mappingMenuItem.menuSectionId = menuSection.id'),
      'itemsCount',
    );

    // Apply sorting
    const sortField = this.getSortField(sortBy);
    queryBuilder.orderBy(sortField, sort);

    // Use getRawAndEntities to preserve addSelect fields
    const totalCount = await queryBuilder.getCount();
    const { entities, raw } = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getRawAndEntities();

    // Map the raw data to entities to include addSelect fields
    const itemsWithCounts = entities.map((entity) => {
      const rawItem = raw.find((r) => r.menuSection_id === entity.id);
      entity.itemsCount = parseInt(rawItem.itemsCount || '0', 10);
      return entity;
    });

    return {
      items: itemsWithCounts,
      meta: {
        totalItems: totalCount,
        itemCount: itemsWithCounts.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Get the SQL field name for sorting
   * @param sortBy The sort field from DTO
   */
  private getSortField(sortBy: MenuSectionSortBy): string {
    switch (sortBy) {
      case MenuSectionSortBy.PUBLISHED_NAME:
        return 'menuSection.publishedName';
      case MenuSectionSortBy.INTERNAL_NAME:
        return 'menuSection.internalName';
      case MenuSectionSortBy.UPDATED_AT:
        return 'menuSection.updatedAt';
      case MenuSectionSortBy.ACTIVE_AT:
        return 'menuSection.activeAt';
      case MenuSectionSortBy.ITEMS_COUNT:
        return '"itemsCount"';
      default:
        return 'menuSection.updatedAt';
    }
  }

  async findOne(
    id: string,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
    relations?: boolean,
  ): Promise<MenuSection> {
    await this.checkPermissionById(id, user, permissionRoles);

    return this.findOneById(id, relations);
  }

  async findOneById(id: string, relations?: boolean): Promise<MenuSection> {
    const queryBuilder = this.menuSectionRepository
      .createQueryBuilder('menuSection')
      .where('menuSection.id = :id', { id });

    if (relations) {
      queryBuilder
        .leftJoinAndSelect('menuSection.restaurant', 'restaurant')
        .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
        .leftJoinAndSelect('menuSection.mappingMenuItems', 'mappingMenuItems')
        .leftJoinAndSelect('mappingMenuItems.menuItem', 'menuItem', 'menuItem.type = :itemType', {
          itemType: MenuItemType.ITEM,
        })
        .leftJoinAndSelect('menuSection.mappingMenus', 'mappingMenus')
        .leftJoinAndSelect('mappingMenus.menu', 'menu');
    }

    const menuSection = await queryBuilder.getOne();

    if (!menuSection) {
      throw new NotFoundException(`Menu section not found or you don't have access to it`);
    }
    if (relations) menuSection.itemsCount = menuSection.menuItems?.length ?? 0;
    return menuSection;
  }

  async checkPermissionById(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<void> {
    const menuSection = await this.findOneById(id);
    await this.permissionMerchantUserService.verifyAccessRestaurant(menuSection.restaurantId, user, permissionRoles);
  }

  async update(
    id: string,
    updateMenuSectionDto: UpdateMenuSectionDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<MenuSection> {
    const menuSection = await this.findOne(id, user, permissionRoles);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      menuSection.restaurantId,
      updateMenuSectionDto.internalName,
      updateMenuSectionDto.publishedName,
      id,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemIds, menuIds and availableSchedule from DTO if present
    const { menuItemIds, menuIds, isActive, scheduleActiveAt, availableSchedule, ...menuSectionData } =
      updateMenuSectionDto;

    // Update basic properties
    Object.assign(menuSection, menuSectionData);
    delete menuSection.mappingMenuItems;
    await this.dataSource.transaction(async (entityManager) => {
      // Handle available schedule if provided
      await this.handleAvailableSchedule(menuSection, availableSchedule, entityManager);

      // Handle menu item relationships
      await this.handleMenuItemRelationship(menuSection, menuItemIds, entityManager);

      // Handle menu relationships
      await this.handleMenuRelationship(menuSection, menuIds, entityManager);

      const menuWasActive = !isNil(menuSection.activeAt);
      // Handle isActive and scheduleActiveAt logic
      if (!isNil(isActive) && menuWasActive !== isActive) {
        menuSection.activeAt = isActive ? new Date() : null;
      }

      if (!menuSection.activeAt && scheduleActiveAt !== undefined) {
        menuSection.scheduleActiveAt = scheduleActiveAt ? new Date(scheduleActiveAt) : null;
      }

      // Save the updated entity
      await entityManager.save(menuSection);
    });

    return this.findOneById(id, true);
  }

  async getListByRestaurantId(restaurantId: string, menuSectionIds: string[]) {
    return this.menuSectionRepository.find({ where: { id: In(menuSectionIds), restaurantId } });
  }

  /**
   * Helper method to handle menu item relationships for menu sections
   * @param menuSection The menu section entity to update
   * @param menuItemIds Array of menu item IDs to link, or undefined to skip updating
   */
  private async handleMenuItemRelationship(
    menuSection: MenuSection,
    menuItemIds: PositionItemWithPricePositiveDto[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemIds is undefined, don't update menuItems
    if (menuItemIds === undefined) {
      return;
    }

    // Clear existing mapping relationships
    if (menuSection.id) {
      await entityManager.delete(MappingMenuSectionMenuItem, { menuSectionId: menuSection.id });
    }

    // If menuItemIds is an empty array, we're done (all relationships cleared)
    if (menuItemIds.length === 0) {
      return;
    }

    // Extract IDs from PositionItemWithPricePositiveDto array
    const ids = menuItemIds.map((item) => item.id);

    // Find menu items that match both the provided IDs and the restaurant ID
    const existingMenuItems = await entityManager.find(MenuItem, {
      where: { id: In(ids), restaurantId: menuSection.restaurantId },
      select: ['id', 'type'],
    });

    // Check if all menu item IDs exist and belong to the restaurant
    if (existingMenuItems.length !== ids.length) {
      const foundIds = existingMenuItems.map((menuItem) => menuItem.id);
      const missingIds = ids.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following menu item IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Validate that all menu items have type 'item' (not 'option')
    const invalidTypeMenuItems = existingMenuItems.filter((menuItem) => menuItem.type !== MenuItemType.ITEM);
    if (invalidTypeMenuItems.length > 0) {
      const invalidIds = invalidTypeMenuItems.map((menuItem) => menuItem.id);
      throw new BadRequestException(
        `Menu sections can only contain menu items with type '${MenuItemType.ITEM}'. The following menu item IDs have invalid type: ${invalidIds.join(', ')}`,
      );
    }

    // Create mapping relationships with positions and price
    if (menuSection.id) {
      const mappingData = menuItemIds.map((item) =>
        entityManager.create(MappingMenuSectionMenuItem, {
          menuSectionId: menuSection.id,
          menuItemId: item.id,
          position: item.position,
          price: item.price,
        }),
      );
      await entityManager.save(mappingData);
    }
  }

  /**
   * Helper method to handle available schedule relationships for menu sections
   * @param menuSection The menu section entity to update
   * @param availableSchedule Array of schedule items to link, or undefined to skip updating
   * @param entityManager The entity manager for transaction
   */
  private async handleAvailableSchedule(
    menuSection: MenuSection,
    availableSchedule: ScheduleItem[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If availableSchedule is undefined, don't update schedules
    if (availableSchedule === undefined) {
      return;
    }

    // Clear existing schedule relationships
    if (menuSection.id) {
      await entityManager.delete(MenuSectionAvailableSchedule, { menuSectionId: menuSection.id });
    }

    // If availableSchedule is an empty array, we're done (all schedules cleared)
    if (availableSchedule.length === 0) {
      return;
    }

    // Create new schedule relationships
    if (menuSection.id) {
      const scheduleData = availableSchedule.map((schedule) => {
        // If isAllDay is true, start and end should be null
        const start = schedule.start;
        const end = schedule.end;

        return entityManager.create(MenuSectionAvailableSchedule, {
          menuSectionId: menuSection.id,
          day: schedule.day,
          start,
          end,
          isAllDay: schedule.isAllDay || false,
        });
      });
      await entityManager.save(scheduleData);
    }
  }

  /**
   * Helper method to handle menu relationships for menu sections (add section to menus)
   * @param menuSection The menu section entity to update
   * @param menuIds Array of menu IDs to link, or undefined to skip updating
   */
  private async handleMenuRelationship(
    menuSection: MenuSection,
    menuIds: string[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuIds is undefined, don't update menus
    if (menuIds === undefined) {
      return;
    }

    if (!menuSection.id) {
      return;
    }

    // Get existing mappings where this menu section is linked to menus

    const existingMappings = await entityManager.find(MappingMenuMenuSection, {
      where: { menuSectionId: menuSection.id },
      select: ['menuId', 'menuSectionId'],
    });

    // If menuIds is an empty array, remove all existing mappings
    if (menuIds.length === 0) {
      if (existingMappings.length > 0) {
        await entityManager.delete(MappingMenuMenuSection, { menuSectionId: menuSection.id });
      }
      return;
    }

    // Verify all menu IDs exist and belong to the same restaurant
    const existingMenus = await entityManager.find(Menu, {
      where: { id: In(menuIds), restaurantId: menuSection.restaurantId },
      select: ['id'],
    });

    if (existingMenus.length !== menuIds.length) {
      const foundIds = existingMenus.map((menu) => menu.id);
      const missingIds = menuIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `The following menu IDs do not exist or don't belong to this restaurant: ${missingIds.join(', ')}`,
      );
    }

    // Find existing menu IDs that this section is already linked to
    const existingMenuIds = existingMappings.map((mapping) => mapping.menuId);

    // Find mappings to remove (existing but not in new list)
    const mappingsToRemove = existingMappings.filter((mapping) => !menuIds.includes(mapping.menuId));

    // Find menu IDs to add (in new list but not existing)
    const menuIdsToAdd = menuIds.filter((id) => !existingMenuIds.includes(id));

    // Remove mappings that are no longer needed
    if (mappingsToRemove.length > 0) {
      const menuIdsToRemove = mappingsToRemove.map((mapping) => mapping.menuId);
      await entityManager.delete(MappingMenuMenuSection, {
        menuSectionId: menuSection.id,
        menuId: In(menuIdsToRemove),
      });
    }
    // Add new mappings with position calculated via raw query
    if (menuIdsToAdd.length > 0) {
      const values = menuIdsToAdd
        .map(
          (menuId) =>
            `('${menuId}', '${menuSection.id}', COALESCE((SELECT MAX(position) FROM mapping_menus_menu_sections WHERE menu_id = '${menuId}'), 0) + 1)`,
        )
        .join(', ');

      await entityManager.query(`
        INSERT INTO mapping_menus_menu_sections (menu_id, menu_section_id, position)
        VALUES ${values}
      `);
    }
  }

  async checkNameExists(
    restaurantId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      return NameValidationHelper.checkNameExists(
        this.menuSectionRepository,
        'menuSection',
        restaurantId,
        'internalName',
        internalName,
        excludeId,
      );
    }

    if (publishedName) {
      return NameValidationHelper.checkNameExists(
        this.menuSectionRepository,
        'menuSection',
        restaurantId,
        'publishedName',
        publishedName,
        excludeId,
      );
    }

    return false;
  }

  async delete(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<MenuSection> {
    const menuSection = await this.findOne(id, user, permissionRoles);

    await this.dataSource.transaction(async (manager) => {
      // Soft delete all mapping relations first
      await manager.delete(MappingMenuSectionMenuItem, { menuSectionId: menuSection.id });
      await manager.delete(MappingMenuMenuSection, { menuSectionId: menuSection.id });

      // Then soft delete the menu section
      await manager.softDelete(MenuSection, { id: menuSection.id });
    });

    return menuSection;
  }
}
