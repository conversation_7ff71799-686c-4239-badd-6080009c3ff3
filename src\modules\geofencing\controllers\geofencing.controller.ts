import { User } from '@/common/decorators/user.decorator';
import { UserMerchantJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateBulkGeofencingDto } from '../dto/create-geofencing.dto';
import { GeofencingService } from '../geofencing.service';

@ApiTags('geofencing')
@Controller('geofencing')
@Roles({ userType: UserType.MERCHANT_USER })
export class GeofencingController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];
  constructor(private readonly geofencingService: GeofencingService) {}

  @Post()
  @ApiOperation({ summary: 'Create/Update geofencing areas' })
  async create(@Body() createGeofencingDto: CreateBulkGeofencingDto, @User() user: UserMerchantJwtInfo) {
    return this.geofencingService.createOrUpdateBulk(createGeofencingDto, user, this.permissionRoles);
  }

  @Get('restaurant/:restaurantId')
  @ApiOperation({ summary: 'Get all geofencing areas for a restaurant' })
  async findByRestaurant(@Param('restaurantId') restaurantId: string, @User() user: UserMerchantJwtInfo) {
    return this.geofencingService.findByRestaurant(restaurantId, user, this.permissionRoles);
  }
}
