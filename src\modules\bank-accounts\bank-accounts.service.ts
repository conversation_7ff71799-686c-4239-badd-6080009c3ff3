import { Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { ReconciliationsService } from '@/modules/reconciliations/reconciliations.service';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { BadRequestException, forwardRef, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { UserMerchantJwtInfo } from '../auth/types/jwt-payload.type';
import { CreateBankAccountDto } from './dtos/create-bank-account.dto';
import { ListBankAccountDto } from './dtos/list-bank-account.dto';
import { UpdateBankAccountDto } from './dtos/update-bank-account.dto';
import { BankAccount } from './entities/bank-account.entity';

@Injectable()
export class BankAccountsService {
  constructor(
    @InjectRepository(BankAccount)
    private bankAccountRepository: Repository<BankAccount>,
    @Inject(forwardRef(() => ReconciliationsService))
    private reconciliationsService: WrapperType<ReconciliationsService>,
    private permissionMerchantUserService: PermissionMerchantUserService,
  ) {}

  async create(
    createBankAccountDto: CreateBankAccountDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<BankAccount> {
    const hasBrand = !!createBankAccountDto.brandId;
    const hasRestaurant = !!createBankAccountDto.restaurantId;
    if ((hasBrand && hasRestaurant) || (!hasBrand && !hasRestaurant)) {
      throw new BadRequestException('Only one of brandId or restaurantId must be provided.');
    }
    if (createBankAccountDto.restaurantId) {
      await this.permissionMerchantUserService.verifyAccessRestaurant(
        createBankAccountDto.restaurantId,
        user,
        permissionRoles,
      );
    }
    if (createBankAccountDto.brandId) {
      await this.permissionMerchantUserService.verifyAccessBrand(createBankAccountDto.brandId, user, permissionRoles);
    }
    const bankAccount = this.bankAccountRepository.create(createBankAccountDto);
    return this.bankAccountRepository.save(bankAccount);
  }

  async findAll(
    listBankAccountDto: ListBankAccountDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ) {
    const { page, limit, brandId } = listBankAccountDto;

    await this.permissionMerchantUserService.verifyAccessBrand(brandId, user, permissionRoles);

    const queryBuilder = this.bankAccountRepository
      .createQueryBuilder('bankAccount')
      .leftJoinAndSelect('bankAccount.restaurant', 'restaurant')
      .leftJoinAndSelect('bankAccount.brand', 'brand');

    queryBuilder.andWhere('(bankAccount.brandId = :brandId OR restaurant.brandId = :brandId)', { brandId });

    queryBuilder.orderBy('bankAccount.createdAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<BankAccount> {
    const bankAccount = await this.bankAccountRepository.findOne({
      where: { id },
      relations: ['restaurant', 'brand'],
    });

    if (!bankAccount) {
      throw new NotFoundException(`Bank account not found`);
    }

    if (bankAccount.brandId && bankAccount.restaurantId) {
      throw new BadRequestException('Bank account cannot belong to both brand and restaurant.');
    }

    // Check access to brand/restaurant of this bank account
    if (bankAccount.brandId) {
      await this.permissionMerchantUserService.verifyAccessBrand(bankAccount.brandId, user, permissionRoles);
    } else if (bankAccount.restaurantId) {
      await this.permissionMerchantUserService.verifyAccessRestaurant(bankAccount.restaurantId, user, permissionRoles);
    } else {
      throw new BadRequestException('Bank account is not linked to any brand or restaurant.');
    }

    return bankAccount;
  }

  async findOneById(id: string): Promise<BankAccount> {
    const bankAccount = await this.bankAccountRepository.findOne({ where: { id } });
    if (!bankAccount) {
      throw new NotFoundException(`Bank account not found`);
    }
    return bankAccount;
  }

  async update(
    id: string,
    updateBankAccountDto: UpdateBankAccountDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<BankAccount> {
    const bankAccount = await this.findOne(id, user, permissionRoles);
    Object.assign(bankAccount, updateBankAccountDto);

    return this.bankAccountRepository.save(bankAccount);
  }

  async remove(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]) {
    const bankAccount = await this.findOne(id, user, permissionRoles);
    // Check if this bank account is referenced in any reconciliation (bank_id or next_bank_id)
    const isReferenced = await this.reconciliationsService.isBankAccountReferenced(id);
    if (isReferenced) {
      throw new BadRequestException('Cannot delete this bank account because it is linked to a reconciliation.');
    }
    await this.bankAccountRepository.softDelete(id);
    return bankAccount;
  }
}
