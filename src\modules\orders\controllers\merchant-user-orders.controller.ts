import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Controller, Get, Param, ParseUUIDPipe, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { OrderQueryDto } from '../dto/order-query.dto';
import { OrdersService } from '../orders.service';

@ApiTags('(Merchant User) Orders')
@Controller('merchant-user/orders')
@Roles({ userType: UserType.MERCHANT_USER })
export class MerchantUserOrdersController {
  constructor(private readonly ordersService: OrdersService) {}
  private readonly permissionRoles = [
    MerchantUserRole.OWNER,
    MerchantUserRole.ADMIN,
    MerchantUserRole.MANAGER,
    MerchantUserRole.ACCOUNTANT,
  ];

  @Get()
  async findAll(@Query() query: OrderQueryDto, @User() user: UserMerchantJwtInfo) {
    return this.ordersService.findAll(query, undefined, undefined, true, user, this.permissionRoles);
  }

  @Get(':id')
  async findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo) {
    return this.ordersService.findOne(id, { userMerchant: user, permissionRoles: this.permissionRoles });
  }
}
