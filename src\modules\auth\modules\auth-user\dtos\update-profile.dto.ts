import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString, Length } from 'class-validator';

import { RemoveLeadingZeroPhone, ToLowerCase } from '@/common/decorators/transforms.decorator';
import { IsPhoneCountryCode } from '@/common/validators/phone-country-code.validator';
import { IsPhoneNumber } from '@/common/validators/phone-number.validator';
import { ApiProperty } from '@nestjs/swagger';

export class RequestUpdateEmailDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @ToLowerCase()
  newEmail: string;
}

export class VerifyUpdateEmailDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  @IsNotEmpty()
  @ToLowerCase()
  newEmail: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6)
  otp: string;
}

export class RequestUpdatePhoneDto {
  @ApiProperty({ example: '987654321' })
  @IsPhoneNumber()
  @IsNotEmpty()
  @RemoveLeadingZeroPhone()
  newPhone: string;

  @ApiProperty({ example: '+84' })
  @IsPhoneCountryCode()
  @IsNotEmpty()
  newPhoneCountryCode: string;
}

export class VerifyUpdatePhoneDto {
  @ApiProperty({ example: '987654321' })
  @IsPhoneNumber()
  @IsNotEmpty()
  @RemoveLeadingZeroPhone()
  newPhone: string;

  @ApiProperty({ example: '+84' })
  @IsPhoneCountryCode()
  @IsNotEmpty()
  newPhoneCountryCode: string;

  @ApiProperty({ example: '123456' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6)
  otp: string;
}
