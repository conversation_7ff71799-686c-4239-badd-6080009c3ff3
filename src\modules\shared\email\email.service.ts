import { Queue } from 'bullmq';

import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';

import { SendOtpEmailJob } from './email.processor';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  constructor(@InjectQueue('email') private emailQueue: Queue<SendOtpEmailJob>) {}

  async queueOtpEmail(to: string, code: string, otpExpiryMinutes: number, locale: string = 'en'): Promise<void> {
    try {
      const jobId = `otp-${to}-${Date.now()}`;
      await this.emailQueue.add(
        'send-otp',
        {
          to,
          code,
          otpExpiryMinutes,
          locale,
        },
        {
          jobId,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
          removeOnComplete: 10,
          removeOnFail: 5,
        },
      );
      this.logger.log(`OTP email queued for ${to} with job ID: ${jobId}`);
    } catch (error) {
      this.logger.error(`Failed to queue OTP email for ${to}:`, error);
      throw new Error(`Failed to queue email: ${error.message}`);
    }
  }
}
