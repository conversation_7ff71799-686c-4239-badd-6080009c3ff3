import { ABRole } from '@/modules/admins/enums/admin-role.enum';
import { applyDecorators, SetMetadata } from '@nestjs/common';
import { ApiSecurity } from '@nestjs/swagger';

import { UserType } from '../enums/user-type.enum';

export const ROLES_KEY = 'roles';

export type RoleType = any; // Define the role types for each user type
type MerchantUserRoles = { userType: UserType.MERCHANT_USER };
type MerchantStaffRoles = { userType: UserType.MERCHANT_STAFF; role?: '*' };
type AdminRoles = { userType: UserType.AB_ADMIN; role?: ABRole[] | '*' };
type UserRoles = { userType: UserType.USER; role?: '*' };

export type RoleKey = MerchantUserRoles | MerchantStaffRoles | AdminRoles | UserRoles;

export function Roles(...roleKeys: RoleKey[]) {
  const roleKeysString = roleKeys
    .map((roleKey) => {
      const listRoles = 'role' in roleKey ? (typeof roleKey.role === 'string' ? [roleKey.role] : roleKey.role) : ['*'];
      return listRoles?.map((role) => `${roleKey.userType}:${role}`) ?? [];
    })
    .flat();
  return applyDecorators(SetMetadata(ROLES_KEY, roleKeysString), ApiSecurity('roles', roleKeysString));
}
