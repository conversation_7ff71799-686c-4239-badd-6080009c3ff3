import { BullModule } from '@nestjs/bullmq';
import { Global, Module } from '@nestjs/common';

import { SmsProcessor } from './sms.processor';
import { SmsService } from './sms.service';

@Global() // Make services available globally
@Module({
  imports: [
    BullModule.registerQueue({
      name: 'sms',
    }),
  ],
  providers: [SmsService, SmsProcessor],
  exports: [SmsService],
})
export class SmsModule {}
