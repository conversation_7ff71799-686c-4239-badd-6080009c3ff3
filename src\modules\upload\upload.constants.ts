import { ImageDimensions } from './upload.types';

export enum FolderType {
  MENU_ITEMS = 'menu-items',
  RESTAURANT_BANNER = 'restaurant-banner',
  RESTAURANT_AVATAR = 'restaurant-avatar',
  BRAND_LOGO = 'brand-logo',
}

export const FOLDER_DIMENSIONS: Record<FolderType, ImageDimensions> = {
  [FolderType.MENU_ITEMS]: {
    minWidth: 1024,
    minHeight: 1024,
    aspectRatio: { width: 16, height: 9 },
  },
  [FolderType.RESTAURANT_BANNER]: {
    minWidth: 1024,
    minHeight: 1024,
    aspectRatio: { width: 1, height: 1 },
  },
  [FolderType.RESTAURANT_AVATAR]: {
    minWidth: 512,
    minHeight: 512,
    aspectRatio: { width: 1, height: 1 },
  },
  [FolderType.BRAND_LOGO]: {
    minWidth: 512,
    minHeight: 512,
    aspectRatio: { width: 1, height: 1 },
  },
};
