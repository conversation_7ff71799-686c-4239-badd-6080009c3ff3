import { Exclude } from 'class-transformer';
import { CreateDateColumn, DeleteDateColumn, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export abstract class BaseEntityWithoutId {
  @Index({ where: 'deleted_at IS NULL' })
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @Index({ where: 'deleted_at IS NULL' })
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Exclude()
  @Index()
  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt?: Date | null;
}

export abstract class BaseEntity extends BaseEntityWithoutId {
  @PrimaryGeneratedColumn('uuid')
  id: string;
}
