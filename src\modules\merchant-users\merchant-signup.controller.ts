import { Public } from '@/common/decorators/public.decorator';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { MerchantSignupDto } from './dtos/merchant-signup.dto';
import { MerchantUser } from './entities/merchant-user.entity';
import { MerchantUsersService } from './merchant-users.service';

export class MerchantSignupResponseDto {
  merchantUser: MerchantUser;
  brand: Brand;
  restaurant: Restaurant;
}

@ApiTags('Merchant Signup')
@Controller('merchant-signup')
export class MerchantSignupController {
  constructor(private readonly merchantUsersService: MerchantUsersService) {}

  @Public()
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ 
    summary: 'Merchant sign-up',
    description: 'Creates a merchant user, brand, and restaurant in a single atomic operation. All entities are created with inactive status by default.'
  })
  async signup(@Body() signupDto: MerchantSignupDto): Promise<MerchantSignupResponseDto> {
    return this.merchantUsersService.signup(signupDto);
  }
}
