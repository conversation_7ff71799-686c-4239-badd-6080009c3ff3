import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { Restaurant } from './restaurant.entity';

@Entity('restaurant_available_schedules')
export class RestaurantAvailableSchedule extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid' })
  restaurantId: string;

  @ManyToOne(() => Restaurant, (restaurant) => restaurant.availableSchedule, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'restaurant_id' })
  restaurant: WrapperType<Restaurant>;

  // Day of week (0=Sunday, 1=Monday, ..., 6=Saturday)
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'day', type: 'int' })
  day: number;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'start', type: 'time', default: '00:00:00' })
  start: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'end', type: 'time', default: '23:59:59' })
  end: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'is_all_day', type: 'boolean', default: false })
  isAllDay: boolean;
}
