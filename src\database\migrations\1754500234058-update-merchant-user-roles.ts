import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateMerchantUserRoles1754500234058 implements MigrationInterface {
  name = 'UpdateMerchantUserRoles1754500234058';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "merchant_accounts" DROP CONSTRAINT "FK_b132a6f2a24dabfce5bd3e8d5d5"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_a3da54c4ede1902c343681ced3"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_eb77981778960cdbe11559d16c"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "role"`);
    await queryRunner.query(`DROP TYPE "public"."merchant_users_role_enum"`);
    await queryRunner.query(
      `CREATE TYPE "public"."merchant_user_permissions_role_enum" AS ENUM('owner', 'admin', 'manager', 'accountant')`,
    );
    await queryRunner.query(
      `CREATE TABLE "merchant_user_permissions" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "merchant_user_id" uuid NOT NULL, "merchant_account_id" uuid, "restaurant_id" uuid, "role" "public"."merchant_user_permissions_role_enum" NOT NULL, CONSTRAINT "PK_3400ee4fc217039eb1dd4a16d92" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_85a3e263a14350240a0f418510" ON "merchant_user_permissions" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f9510e2ef92707a9aa04a6e680" ON "merchant_user_permissions" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4014c6a5dd7cb5e01093a3524b" ON "merchant_user_permissions" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c56af353a265bb0a88aa9d206" ON "merchant_user_permissions" ("merchant_user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9fbd8b28203fc8bc9205d987b9" ON "merchant_user_permissions" ("merchant_account_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dc47f390ee60c9473d3e1a716a" ON "merchant_user_permissions" ("restaurant_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`ALTER TABLE "merchant_accounts" DROP COLUMN "owner_merchant_user_id"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "isSuperAdmin" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(
      `CREATE INDEX "IDX_0fc862c1bc1a2b39c59b2675aa" ON "merchant_users" ("isSuperAdmin") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_user_permissions" ADD CONSTRAINT "FK_1f3789d7ac9b2c46a3756c8a2bf" FOREIGN KEY ("merchant_user_id") REFERENCES "merchant_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_user_permissions" ADD CONSTRAINT "FK_f335170024ca699849de8e90fc2" FOREIGN KEY ("merchant_account_id") REFERENCES "merchant_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_user_permissions" ADD CONSTRAINT "FK_3ae90677738eaefe896f84d221d" FOREIGN KEY ("restaurant_id") REFERENCES "restaurants"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "merchant_user_permissions" DROP CONSTRAINT "FK_3ae90677738eaefe896f84d221d"`);
    await queryRunner.query(`ALTER TABLE "merchant_user_permissions" DROP CONSTRAINT "FK_f335170024ca699849de8e90fc2"`);
    await queryRunner.query(`ALTER TABLE "merchant_user_permissions" DROP CONSTRAINT "FK_1f3789d7ac9b2c46a3756c8a2bf"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_0fc862c1bc1a2b39c59b2675aa"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "isSuperAdmin"`);
    await queryRunner.query(
      `CREATE TYPE "public"."merchant_users_role_enum" AS ENUM('super-admin', 'admin', 'member')`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_users" ADD "role" "public"."merchant_users_role_enum" NOT NULL DEFAULT 'member'`,
    );

    await queryRunner.query(`ALTER TABLE "merchant_accounts" ADD "owner_merchant_user_id" uuid`);
    await queryRunner.query(`DROP INDEX "public"."IDX_dc47f390ee60c9473d3e1a716a"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9fbd8b28203fc8bc9205d987b9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7c56af353a265bb0a88aa9d206"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4014c6a5dd7cb5e01093a3524b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_f9510e2ef92707a9aa04a6e680"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_85a3e263a14350240a0f418510"`);
    await queryRunner.query(`DROP TABLE "merchant_user_permissions"`);
    await queryRunner.query(`DROP TYPE "public"."merchant_user_permissions_role_enum"`);

    await queryRunner.query(
      `CREATE INDEX "IDX_eb77981778960cdbe11559d16c" ON "merchant_accounts" ("owner_merchant_user_id") WHERE (deleted_at IS NULL)`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a3da54c4ede1902c343681ced3" ON "merchant_users" ("role") WHERE (deleted_at IS NULL)`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_accounts" ADD CONSTRAINT "FK_b132a6f2a24dabfce5bd3e8d5d5" FOREIGN KEY ("owner_merchant_user_id") REFERENCES "merchant_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
