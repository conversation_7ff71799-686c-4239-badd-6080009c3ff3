import { IsNotEmpty, IsUUID } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';

export class CloneGeofencingDto {
  @ApiProperty({
    description: 'ID of the source restaurant (geofencing will be copied from this restaurant)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  sourceRestaurantId: string;

  @ApiProperty({
    description: 'ID of the target restaurant (geofencing will be copied to this restaurant)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty()
  @IsUUID()
  targetRestaurantId: string;
}
