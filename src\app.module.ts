import { Accept<PERSON><PERSON><PERSON>ge<PERSON><PERSON><PERSON>ver, CookieResolver, HeaderResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import * as path from 'path';

import { AppMode } from '@/common/enums/event-pattern.enum';
import { DisableRoutesInterceptor } from '@/common/interceptors/disable-routes.interceptor';
import appConfig from '@/config/app.config';
import authConfig from '@/config/auth.config';
import emailConfig from '@/config/email.config';
import fcmConfig from '@/config/fcm.config';
import otpConfig from '@/config/otp.config';
import redisConfig from '@/config/redis.config';
import smsConfig from '@/config/sms.config';
import typeormConfig from '@/config/typeorm.config';
import { AdminsModule } from '@/modules/admins/admins.module';
import { AdminsService } from '@/modules/admins/admins.service';
import { AppVersionModule } from '@/modules/app-version/app-version.module';
import { BankAccountsModule } from '@/modules/bank-accounts/bank-accounts.module';
import { BrandsModule } from '@/modules/brands/brands.module';
import { CartsModule } from '@/modules/carts/carts.module';
import { ChatModule } from '@/modules/chat/chat.module';
import { GeofencingModule } from '@/modules/geofencing/geofencing.module';
import { GoogleMapsModule } from '@/modules/google-maps/google-maps.module';
import { IngredientsModule } from '@/modules/ingredients/ingredients.module';
import { MenuItemOptionGroupsModule } from '@/modules/menu-item-option-groups/menu-item-option-groups.module';
import { MenuItemsModule } from '@/modules/menu-items/menu-items.module';
import { MenuSectionsModule } from '@/modules/menu-sections/menu-sections.module';
import { MenusModule } from '@/modules/menus/menus.module';
import { MerchantStaffModule } from '@/modules/merchant-staff/merchant-staff.module';
import { MerchantUsersModule } from '@/modules/merchant-users/merchant-users.module';
import { OnepayModule } from '@/modules/onepay/onepay.module';
import { OrdersModule } from '@/modules/orders/orders.module';
import { OtpModule } from '@/modules/otp/otp.module';
import { PaymentCardModule } from '@/modules/payment-card/payment-card.module';
import { ReconciliationsModule } from '@/modules/reconciliations/reconciliations.module';
import { RestaurantReviewsModule } from '@/modules/restaurant-reviews/restaurant-reviews.module';
import { RestaurantTagsModule } from '@/modules/restaurant-tags/restaurant-tags.module';
import { RestaurantsModule } from '@/modules/restaurants/restaurants.module';
import { ReviewTagsModule } from '@/modules/review-tags/review-tags.module';
import { SchedulesModule } from '@/modules/schedules/schedules.module';
import { SharedModule } from '@/modules/shared/shared.module';
import { TranslatesModule } from '@/modules/translates/translates.module';
import uploadConfig from '@/modules/upload/config/upload.config';
import { UploadModule } from '@/modules/upload/upload.module';
import { UserAddressesModule } from '@/modules/user-addresses/user-addresses.module';
import { UserFavouriteRestaurantsModule } from '@/modules/user-favourite-restaurants/user-favourite-restaurants.module';
import { UsersModule } from '@/modules/users/users.module';
import { AuthModule } from '@auth/auth.module';
import { BullModule } from '@nestjs/bullmq';
import { Module, OnApplicationBootstrap } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';

const modules = [
  ConfigModule.forRoot({
    isGlobal: true, // Make ConfigModule available globally
    load: [
      appConfig,
      typeormConfig,
      emailConfig,
      redisConfig,
      authConfig,
      otpConfig,
      uploadConfig,
      fcmConfig,
      smsConfig,
    ], // Load all configurations
  }),
  TypeOrmModule.forRootAsync({
    inject: [ConfigService],
    useFactory: (configService: ConfigService) => {
      const dbConfig = configService.get<TypeOrmModuleOptions>('typeorm');
      if (!dbConfig) {
        throw new Error('TypeORM configuration not found');
      }
      return dbConfig;
    },
  }),
  BullModule.forRootAsync({
    inject: [ConfigService],
    useFactory: (configService: ConfigService) => ({
      connection: {
        host: configService.get<string>('redis.host', 'localhost'),
        port: configService.get<number>('redis.port', 6379),
        password: configService.get<string>('redis.password'),
      },
    }),
  }),
  I18nModule.forRoot({
    fallbackLanguage: 'en',
    loaderOptions: {
      path: path.join(__dirname, '/i18n/'),
      watch: true,
    },
    resolvers: [
      new QueryResolver(['lang', 'l']),
      new HeaderResolver(['x-custom-lang']),
      new CookieResolver(),
      AcceptLanguageResolver,
    ],
  }),
  // Global modules,
  SharedModule,
  UploadModule,
  AuthModule,
  UsersModule,
  AdminsModule,
  AppVersionModule,
  MerchantUsersModule,
  MerchantStaffModule,
  BrandsModule,
  RestaurantsModule,
  RestaurantReviewsModule,
  ReviewTagsModule,
  MenusModule,
  MenuSectionsModule,
  MenuItemsModule,
  MenuItemOptionGroupsModule,
  IngredientsModule,
  RestaurantTagsModule,
  TranslatesModule,
  CartsModule,
  OrdersModule,
  ChatModule,
  PaymentCardModule,
  OnepayModule,
  UserAddressesModule,
  UserFavouriteRestaurantsModule,
  GoogleMapsModule,
  GeofencingModule,
  OtpModule,
  BankAccountsModule,
  ReconciliationsModule,
];

if ([AppMode.GENERAL, AppMode.JOB].includes((process.env.APP_MODE ?? AppMode.GENERAL) as AppMode)) {
  modules.push(SchedulesModule);
}

@Module({
  imports: modules,
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: DisableRoutesInterceptor,
    },
  ],
})
export class AppModule implements OnApplicationBootstrap {
  constructor(private readonly adminsService: AdminsService) {}

  async onApplicationBootstrap() {
    await this.adminsService.ensureRootAdmin();
  }
}
