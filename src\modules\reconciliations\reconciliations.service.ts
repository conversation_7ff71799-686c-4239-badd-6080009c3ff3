import { Entity<PERSON>anager, Repository } from 'typeorm';

import { UserMerchantJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { BankAccountsService } from '@/modules/bank-accounts/bank-accounts.service';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ReconciliationResponseDto } from './dtos/reconciliation-response.dto';
import { UpsertReconciliationDto } from './dtos/upsert-reconciliation.dto';
import { Reconciliation } from './entities/reconciliation.entity';
import { calculateNextReconciliationPeriod, getDayjsTimeWithTz } from './reconciliations.helper';

import type { BankAccount } from '../bank-accounts/entities/bank-account.entity';
@Injectable()
export class ReconciliationsService {
  constructor(
    @InjectRepository(Reconciliation)
    private reconciliationRepository: Repository<Reconciliation>,

    @Inject(forwardRef(() => BankAccountsService))
    private bankAccountsService: WrapperType<BankAccountsService>,

    private permissionMerchantUserService: PermissionMerchantUserService,
  ) {}

  async update(
    upsertReconciliationDto: UpsertReconciliationDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ) {
    const { restaurantId, settlementPlan, bankId } = upsertReconciliationDto;

    // Verify restaurant access
    const restaurant = await this.permissionMerchantUserService.verifyAccessRestaurant(
      restaurantId,
      user,
      permissionRoles,
    );

    let bankAccount: BankAccount | null = null;
    // Verify bank account exists only if bankId is provided
    if (bankId) {
      bankAccount = await this.bankAccountsService.findOneById(bankId);

      // Check if bankAccount belongs to the same restaurant or brand as the reconciliation
      if (
        (bankAccount?.restaurantId && bankAccount.restaurantId !== restaurant.id) ||
        (bankAccount?.brandId && bankAccount.brandId !== restaurant.brandId)
      ) {
        throw new BadRequestException('Bank account must belong to the same restaurant or brand as the reconciliation');
      }
    }

    // Find existing reconciliation for this restaurant
    const reconciliation = await this.reconciliationRepository.findOne({ where: { restaurantId } });

    if (!reconciliation) {
      throw new BadRequestException('Reconciliation not found for this restaurant');
    }

    if (bankId !== undefined) {
      reconciliation.bankId = bankId;
    }

    // Update logic based on startTime and endTime
    if (!reconciliation.startTime || !reconciliation.endTime) {
      // If startTime and endTime are not set, update directly
      reconciliation.settlementPlan = settlementPlan;
    } else {
      // If startTime and endTime are set, update next fields
      reconciliation.nextSettlementPlan = settlementPlan;
    }

    const savedReconciliation = await this.reconciliationRepository.save(reconciliation);

    const result = await this.reconciliationRepository.findOne({
      where: { id: savedReconciliation.id },
      relations: ['restaurant', 'bankAccount'],
    });

    if (!result) {
      throw new BadRequestException('Reconciliation not found for this restaurant');
    }

    return {
      ...restaurant,
      reconciliation: ReconciliationResponseDto.fromEntity(result),
    };
  }

  async isBankAccountReferenced(bankAccountId: string): Promise<boolean> {
    return this.reconciliationRepository
      .createQueryBuilder('reconciliation')
      .where('reconciliation.bankId = :id', { id: bankAccountId })
      .getExists();
  }

  /**
   * Create default reconciliation for restaurant when created
   * @param restaurantId The restaurant ID
   */
  async createDefaultReconciliation(restaurantId: string, manager: EntityManager): Promise<Reconciliation> {
    // Check if reconciliation already exists
    const existingReconciliation = await this.reconciliationRepository.findOne({
      where: { restaurantId },
    });

    if (existingReconciliation) {
      return existingReconciliation;
    }

    const reconciliation = this.reconciliationRepository.create({ restaurantId });

    return manager.save(Reconciliation, reconciliation);
  }

  /**
   * Update startTime and endTime when activating menu for the first time
   * @param restaurantId The restaurant ID
   */
  async activeReconciliation(restaurantId: string, manager: EntityManager): Promise<void> {
    let reconciliation = await this.reconciliationRepository.findOne({
      where: { restaurantId },
    });

    if (!reconciliation) {
      reconciliation = await this.createDefaultReconciliation(restaurantId, manager);
    }

    // Only update startTime and endTime if they are not set
    if (reconciliation.startTime && reconciliation.endTime) return;

    const startTime = getDayjsTimeWithTz();
    reconciliation.startTime = startTime.toDate();
    reconciliation.endTime = calculateNextReconciliationPeriod(
      startTime.toDate(),
      reconciliation.settlementPlan,
    ).endDate;
    reconciliation.nextSettlementPlan = reconciliation.settlementPlan;

    await manager.save(Reconciliation, reconciliation);
  }
}
