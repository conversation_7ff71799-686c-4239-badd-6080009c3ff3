import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Body, Controller, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { MerchantUserRole } from '../merchant-users/enums/merchant-users-role.enum';
import { UpsertReconciliationDto } from './dtos/upsert-reconciliation.dto';
import { ReconciliationsService } from './reconciliations.service';

@ApiTags('Reconciliations')
@Controller('reconciliations')
@Roles({ userType: UserType.MERCHANT_USER })
export class ReconciliationsController {
  constructor(private readonly reconciliationsService: ReconciliationsService) {}
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN];

  @Post('upsert')
  upsert(@Body() upsertReconciliationDto: UpsertReconciliationDto, @User() user: UserMerchantJwtInfo) {
    return this.reconciliationsService.update(upsertReconciliationDto, user, this.permissionRoles);
  }
}
