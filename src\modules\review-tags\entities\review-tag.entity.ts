import { Column, Entity, Index, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MappingRestaurantReviewTag } from '@/modules/restaurant-reviews/entities/mapping-restaurant-review-tag.entity';

import { ReviewTagType } from '../review-tags.enum';

@Entity('review_tags')
export class ReviewTag extends BaseEntity {
  @Column({ name: 'name', type: 'varchar', length: 100 })
  name: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'active', type: 'boolean', default: true })
  active: boolean;

  @OneToMany(() => MappingRestaurantReviewTag, (mappingReviewTag) => mappingReviewTag.reviewTag)
  mappingRestaurantReviewTags?: WrapperType<MappingRestaurantReviewTag>[];

  @Column({ name: 'ratings', type: 'jsonb', default: '[]' })
  ratings: { rating: number; displayOrder: number }[];

  @Column({ name: 'type', type: 'varchar', length: 20 })
  type: ReviewTagType;
}
