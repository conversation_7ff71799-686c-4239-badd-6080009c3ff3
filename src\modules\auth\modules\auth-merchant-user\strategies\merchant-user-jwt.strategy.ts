import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { MerchantUsersService } from '@/modules/merchant-users/merchant-users.service';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo, UserMerchantJwtPayload } from '@auth/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';

@Injectable()
export class MerchantUserJwtStrategy extends PassportStrategy(Strategy, 'merchant-user-jwt') {
  constructor(
    private configService: ConfigService,
    private merchantUsersService: MerchantUsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        ExtractJwt.fromAuthHeaderAsBearerToken(),
        (request: Request) => {
          const accessToken = request?.cookies?.access_token;
          if (!accessToken) {
            return null;
          }
          return accessToken;
        },
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.merchantUserJwtAccessSecret') as string,
    });
  }

  async validate(payload: UserMerchantJwtPayload) {
    const user = await this.merchantUsersService.findById(payload.sub);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.banned) {
      throw new UnauthorizedException('User is banned');
    }

    if (!user.activeAt) {
      throw new UnauthorizedException('User is not active');
    }

    const userInfo: UserMerchantJwtInfo = {
      id: payload.sub,
      email: user.email,
      userType: UserType.MERCHANT_USER,
      isSuperAdmin: user.isSuperAdmin,
    };

    return userInfo;
  }
}
