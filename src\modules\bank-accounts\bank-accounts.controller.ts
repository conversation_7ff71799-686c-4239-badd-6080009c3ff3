import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { UserMerchantJwtInfo } from '../auth/types/jwt-payload.type';
import { MerchantUserRole } from '../merchant-users/enums/merchant-users-role.enum';
import { BankAccountsService } from './bank-accounts.service';
import { CreateBankAccountDto } from './dtos/create-bank-account.dto';
import { ListBankAccountDto } from './dtos/list-bank-account.dto';
import { UpdateBankAccountDto } from './dtos/update-bank-account.dto';
import { BankAccount } from './entities/bank-account.entity';

@ApiTags('Bank Accounts')
@Controller('bank-accounts')
@Roles({ userType: UserType.MERCHANT_USER })
export class BankAccountsController {
  constructor(private readonly bankAccountsService: BankAccountsService) {}
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.ACCOUNTANT];

  @Post()
  create(@Body() createBankAccountDto: CreateBankAccountDto, @User() user: UserMerchantJwtInfo): Promise<BankAccount> {
    return this.bankAccountsService.create(createBankAccountDto, user, this.permissionRoles);
  }

  @Get()
  findAll(
    @Query() listBankAccountDto: ListBankAccountDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Pagination<BankAccount>> {
    return this.bankAccountsService.findAll(listBankAccountDto, user, this.permissionRoles);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<BankAccount> {
    return this.bankAccountsService.findOne(id, user, this.permissionRoles);
  }

  @Patch(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateBankAccountDto: UpdateBankAccountDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<BankAccount> {
    return this.bankAccountsService.update(id, updateBankAccountDto, user, this.permissionRoles);
  }

  @Delete(':id')
  remove(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo) {
    return this.bankAccountsService.remove(id, user, this.permissionRoles);
  }
}
