import { Pagination } from 'nestjs-typeorm-paginate';
import { Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { generateCode } from '@/helpers/string';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { UserType } from '@auth/enums/user-type.enum';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { AdminJwtInfo, UserMerchantJwtInfo } from '../auth/types/jwt-payload.type';
import { MerchantUserRole } from '../merchant-users/enums/merchant-users-role.enum';
import { MerchantUserPermissionsService } from '../merchant-users/merchant-user-permissions.service';
import { AssignBrandOwnerDto } from './dtos/assign-brand-owner.dto';
import { CreateBrandDto } from './dtos/create-brand.dto';
import { ListBrandDto } from './dtos/list-brand.dto';
import { UpdateBrandDto } from './dtos/update-brand.dto';
import { Brand } from './entities/brand.entity';

@Injectable()
export class BrandsService {
  constructor(
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,

    private permissionMerchantUserService: PermissionMerchantUserService,
    private merchantUserPermissionsService: MerchantUserPermissionsService,
  ) {}

  async create(createBrandDto: CreateBrandDto): Promise<Brand> {
    const { ownerMerchantUserId, headquarterLatitude, headquarterLongitude, ...data } = createBrandDto;

    const brandData: any = {
      ...data,
      code: generateCode(3),
      activeAt: new Date(),
    };

    // Set location geometry if coordinates are provided
    if (headquarterLatitude !== undefined && headquarterLongitude !== undefined) {
      brandData.headquarterLatitude = headquarterLatitude;
      brandData.headquarterLongitude = headquarterLongitude;
      brandData.headquarterLocation = {
        type: 'Point',
        coordinates: [headquarterLongitude, headquarterLatitude],
      };
    }

    const brand = this.brandRepository.create(brandData);
    const saved = await this.brandRepository.save(brand);

    if (ownerMerchantUserId) {
      await this.merchantUserPermissionsService.grantBrandOwner(ownerMerchantUserId, saved.id);
    }

    return saved;
  }

  async findAll(listBrandDto: ListBrandDto, user: UserMerchantJwtInfo | AdminJwtInfo): Promise<Pagination<Brand>> {
    const { name, page, limit } = listBrandDto;

    const queryBuilder = this.brandRepository.createQueryBuilder('brand');

    if (user.userType === UserType.MERCHANT_USER && !user.isSuperAdmin) {
      queryBuilder
        .leftJoin('brand.permissions', 'permissions')
        .leftJoin('brand.restaurants', 'restaurants')
        .leftJoin('restaurants.permissions', 'restaurantsPermissions')
        .andWhere('permissions.merchantUserId = :userId OR restaurantsPermissions.merchantUserId = :userId', {
          userId: user.id,
        });
    }

    if (name) {
      queryBuilder.andWhere('brand.name ILIKE :name', { name: `%${name}%` });
    }

    // Order by most recently updated
    queryBuilder.orderBy('brand.updatedAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string, user: AdminJwtInfo | UserMerchantJwtInfo): Promise<Brand> {
    const queryBuilder = this.brandRepository.createQueryBuilder('brand');

    if (user.userType === UserType.MERCHANT_USER && !user.isSuperAdmin) {
      queryBuilder
        .leftJoin('brand.permissions', 'permissions')
        .leftJoin('brand.restaurants', 'restaurants')
        .leftJoin('restaurants.permissions', 'restaurantsPermissions')
        .andWhere('permissions.merchantUserId = :userId OR restaurantsPermissions.merchantUserId = :userId', {
          userId: user.id,
        });
    }

    const brand = await queryBuilder.getOne();

    if (!brand) {
      throw new NotFoundException(`Brand not found`);
    }

    return brand;
  }

  async findOneById(id: string): Promise<Brand> {
    const brand = await this.brandRepository.findOne({ where: { id }, relations: ['restaurants'] });

    if (!brand) {
      throw new NotFoundException(`Brand not found`);
    }

    return brand;
  }

  async update(
    id: string,
    updateBrandDto: UpdateBrandDto,
    user: UserMerchantJwtInfo | AdminJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Brand> {
    await this.permissionMerchantUserService.verifyAccessBrand(id, user, permissionRoles);

    const brand = await this.findOneById(id);

    Object.assign(brand, updateBrandDto);

    return this.brandRepository.save(brand);
  }

  async activate(id: string): Promise<Brand> {
    const brand = await this.findOneById(id);

    await this.brandRepository.update(brand.id, { activeAt: new Date() });

    return this.findOneById(id);
  }

  async deactivate(id: string): Promise<Brand> {
    const brand = await this.findOneById(id);

    await this.brandRepository.update(brand.id, { activeAt: null });

    return this.findOneById(id);
  }

  async assignOwner(dto: AssignBrandOwnerDto) {
    return this.merchantUserPermissionsService.grantBrandOwner(dto.merchantUserId, dto.brandId);
  }
}
