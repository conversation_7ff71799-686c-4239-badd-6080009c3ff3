import { LessThanOrEqual, Repository } from 'typeorm';

import { Reconciliation } from '@/modules/reconciliations/entities/reconciliation.entity';
import {
  calculateNextReconciliationPeriod,
  getDayjsTimeWithTz,
} from '@/modules/reconciliations/reconciliations.helper';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class ReconciliationsSchedulesService {
  private readonly logger = new Logger(ReconciliationsSchedulesService.name);
  private isProcessing = false;

  constructor(
    @InjectRepository(Reconciliation)
    private readonly reconciliationRepository: Repository<Reconciliation>,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handleUpdateReconciliations(): Promise<void> {
    if (this.isProcessing) {
      this.logger.log('Previous reconciliation update is still running, skipping...');
      return;
    }
    this.isProcessing = true;
    try {
      const now = getDayjsTimeWithTz();
      // Only get reconciliations that need to be updated
      const reconciliations = await this.reconciliationRepository.find({
        where: {
          endTime: LessThanOrEqual(now.toDate()),
        },
      });
      for (const rec of reconciliations) {
        if (!rec.endTime || !rec.nextSettlementPlan) continue;
        // Update the necessary fields
        const newStartTime = getDayjsTimeWithTz(rec.endTime);
        if (rec.settlementPlan !== rec.nextSettlementPlan) {
          rec.settlementPlan = rec.nextSettlementPlan;
        }
        // Always recalculate the new start and end time
        rec.startTime = newStartTime.toDate();
        rec.endTime = calculateNextReconciliationPeriod(rec.startTime, rec.settlementPlan).endDate;
        await this.reconciliationRepository.save(rec);
        this.logger.log(`Updated reconciliation ${rec.id} for restaurant ${rec.restaurantId}`);
      }
    } catch (error) {
      this.logger.error('Error in scheduled reconciliation update:', error);
    } finally {
      this.isProcessing = false;
    }
  }
}
