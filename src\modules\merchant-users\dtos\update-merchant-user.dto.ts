import { IsEmail, <PERSON>E<PERSON>, <PERSON>Optional, IsString } from 'class-validator';

import { RemoveLeadingZeroPhone, ToLowerCase } from '@/common/decorators/transforms.decorator';
import { IsPhoneNumber } from '@/common/validators/phone-number.validator';
import { ApiProperty } from '@nestjs/swagger';

import { MerchantUserRole } from '../enums/merchant-users-role.enum';

export class UpdateMerchantUserDto {
  @ApiProperty({ description: 'Email of the merchant user' })
  @IsOptional()
  @IsEmail()
  @ToLowerCase()
  email?: string;

  @ApiProperty({ description: 'First name of the merchant user', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ description: 'Last name of the merchant user', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ description: 'Phone number of the merchant user', required: false })
  @IsOptional()
  @IsPhoneNumber()
  @RemoveLeadingZeroPhone()
  phone?: string;

  @ApiProperty({ description: 'Role of the merchant user', enum: MerchantUserRole, required: false })
  @IsOptional()
  @IsEnum(MerchantUserRole)
  role?: MerchantUserRole;

  @ApiProperty({ description: 'Password of the merchant user', required: false })
  @IsOptional()
  @IsString()
  password?: string;
}
