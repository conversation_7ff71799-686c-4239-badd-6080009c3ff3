import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { UserMerchantJwtInfo } from '../auth/types/jwt-payload.type';
import { CreateMenuSectionDto } from './dtos/create-menu-section.dto';
import { ListMenuSectionDto } from './dtos/list-menu-section.dto';
import { UpdateMenuSectionDto } from './dtos/update-menu-section.dto';
import { MenuSection } from './entities/menu-section.entity';
import { MenuSectionsService } from './menu-sections.service';

@ApiTags('Menu Sections')
@Controller('menu-sections')
@Roles({ userType: UserType.MERCHANT_USER })
export class MenuSectionsController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];
  constructor(private readonly menuSectionsService: MenuSectionsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu section name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menuSectionsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(@Body() createMenuSectionDto: CreateMenuSectionDto, @User() user: UserMerchantJwtInfo): Promise<MenuSection> {
    return this.menuSectionsService.create(createMenuSectionDto, user, this.permissionRoles);
  }

  @Get()
  findAll(
    @Query() listMenuSectionDto: ListMenuSectionDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Pagination<MenuSection>> {
    return this.menuSectionsService.findAll(listMenuSectionDto, user, this.permissionRoles);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<MenuSection> {
    return this.menuSectionsService.findOne(id, user, this.permissionRoles, true);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuSectionDto: UpdateMenuSectionDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<MenuSection> {
    return this.menuSectionsService.update(id, updateMenuSectionDto, user, this.permissionRoles);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a menu section and all its relations' })
  delete(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo): Promise<MenuSection> {
    return this.menuSectionsService.delete(id, user, this.permissionRoles);
  }
}
