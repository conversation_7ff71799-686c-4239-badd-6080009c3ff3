import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { AdminJwtInfo, UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { BrandsService } from './brands.service';
import { AssignBrandOwnerDto } from './dtos/assign-brand-owner.dto';
import { CreateBrandDto } from './dtos/create-brand.dto';
import { ListBrandDto } from './dtos/list-brand.dto';
import { UpdateBrandDto } from './dtos/update-brand.dto';
import { Brand } from './entities/brand.entity';

@ApiTags('Brands')
@Controller('brands')
@Roles({ userType: UserType.AB_ADMIN, role: '*' })
export class BrandsController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN];

  constructor(private readonly brandsService: BrandsService) {}

  @Post()
  create(@Body() createBrandDto: CreateBrandDto): Promise<Brand> {
    return this.brandsService.create(createBrandDto);
  }

  @Roles({ userType: UserType.MERCHANT_USER })
  @Get()
  findAll(
    @Query() listBrandDto: ListBrandDto,
    @User() user: UserMerchantJwtInfo | AdminJwtInfo,
  ): Promise<Pagination<Brand>> {
    return this.brandsService.findAll(listBrandDto, user);
  }

  @Roles({ userType: UserType.MERCHANT_USER })
  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @User() user: UserMerchantJwtInfo | AdminJwtInfo): Promise<Brand> {
    return this.brandsService.findOne(id, user);
  }

  @Put(':id')
  @Roles({ userType: UserType.MERCHANT_USER })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateBrandDto: UpdateBrandDto,
    @User() user: UserMerchantJwtInfo | AdminJwtInfo,
  ): Promise<Brand> {
    return this.brandsService.update(id, updateBrandDto, user, this.permissionRoles);
  }

  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string): Promise<Brand> {
    return this.brandsService.activate(id);
  }

  @Post('assign-owner')
  assignOwner(@Body() dto: AssignBrandOwnerDto) {
    return this.brandsService.assignOwner(dto);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string): Promise<Brand> {
    return this.brandsService.deactivate(id);
  }
}
