import { isNil } from 'lodash';
import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, IsNull, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { getCurrentTimeByTimeAndDay } from '@/helpers/time';
import { UserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import {
  MenuItemOptionGroup,
  MenuItemOptionGroupRule,
} from '@/modules/menu-item-option-groups/entities/menu-item-option-group.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { MenuItemsService } from '@/modules/menu-items/menu-items.service';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { localizeCart } from './carts.helpers';
import { AddToCartDto, CartItemGroupOptionDto } from './dto/add-to-cart.dto';
import { CartQueryDto } from './dto/cart-query.dto';
import { UpdateCartItemAmountDto } from './dto/update-cart-item-amount.dto';
import { CartItemOption } from './entities/cart-item-option.entity';
import { CartItem } from './entities/cart-item.entity';
import { Cart } from './entities/cart.entity';

@Injectable()
export class CartsService {
  constructor(
    @InjectRepository(Cart)
    private readonly cartRepository: Repository<Cart>,
    @InjectRepository(CartItem)
    private readonly cartItemRepository: Repository<CartItem>,
    @InjectRepository(CartItemOption)
    private readonly cartItemOptionRepository: Repository<CartItemOption>,

    private readonly menuItemsService: MenuItemsService,
    private readonly dataSource: DataSource,
  ) {}

  public validateMenuItemBelongsToRestaurant(menuItem: MenuItem, restaurantId: string): void {
    const belongsToRestaurant = menuItem.menuSections?.some((section) =>
      section.menus?.some((menu) => menu.restaurantId === restaurantId),
    );

    if (!belongsToRestaurant) {
      throw new BadRequestException('Menu item does not belong to the specified restaurant');
    }
  }

  public validateMenuItemAvailability(menuItem: MenuItem): void {
    const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();

    const isAvailable = menuItem.menuSections?.some((section) => {
      if (
        !section.availableSchedule ||
        !Array.isArray(section.availableSchedule) ||
        section.availableSchedule.length === 0
      ) {
        return false;
      }

      return section.availableSchedule.some((schedule) => {
        if (schedule.isAllDay) {
          return schedule.day === currentDayOfWeek;
        }

        if (!schedule.start || !schedule.end) {
          return false;
        }

        return currentTime >= schedule.start && currentTime <= schedule.end;
      });
    });

    if (!isAvailable) {
      throw new BadRequestException('Menu item is not available at this time');
    }
  }

  public validateMenuItemOptions(menuItem: MenuItem, groupOptions: CartItemGroupOptionDto[]): void {
    const optionGroups = menuItem.menuItemOptionGroups || [];

    for (const option of groupOptions) {
      const optionGroup = optionGroups.find((group) => group.id === option.optionGroupId);
      if (!optionGroup) {
        throw new BadRequestException(`Option group ${option.optionGroupId} is not valid for this menu item`);
      }
      for (const opt of option.options) {
        const menuItemOption = optionGroup.menuItemOptions?.find((o) => o.id === opt.id);
        if (!menuItemOption) {
          throw new BadRequestException(`Option ${opt.id} is not valid for this menu item`);
        }
      }

      // this.validateMaxAmountOfOption(optionGroup, option);
    }
    // this.validateMenuItemOptionGroup(menuItem, groupOptions);
  }

  validateMaxAmountOfOption(optionGroup: MenuItemOptionGroup, option: CartItemGroupOptionDto): void {
    const optionGroupMaxAmount = optionGroup.maxAmountOfOption;
    for (const optionWithAmountItemDto of option.options) {
      const itemOption = optionGroup.menuItemOptions?.find((o) => o.id === optionWithAmountItemDto.id);
      if (!itemOption) {
        throw new BadRequestException(`Option ${optionWithAmountItemDto.id} is not valid for this menu item`);
      }
      if (
        !isNil(optionWithAmountItemDto?.amount) &&
        !(optionWithAmountItemDto?.amount > 0 && optionWithAmountItemDto?.amount <= optionGroupMaxAmount)
      ) {
        throw new BadRequestException(
          `Option ${optionWithAmountItemDto.id} amount must be between 1 and ${optionGroupMaxAmount}`,
        );
      }
    }
  }

  validateMenuItemOptionGroup(menuItem: MenuItem, groupOptions: CartItemGroupOptionDto[]): void {
    const optionGroups = menuItem.menuItemOptionGroups || [];
    for (const group of optionGroups) {
      const selectedOptions = groupOptions.find((o) => o.optionGroupId === group.id);
      if (!selectedOptions) {
        if (
          group.rule === MenuItemOptionGroupRule.MANDATORY_FIXED ||
          group.rule === MenuItemOptionGroupRule.MANDATORY_RANGE
        ) {
          throw new BadRequestException(`Option group ${group.publishedName} (${group.id}) is mandatory`);
        }
        continue;
      }
      const selectedCount = selectedOptions.options.length;
      switch (group.rule) {
        case MenuItemOptionGroupRule.OPTION:
          // Allow selecting 0 to all options - no validation needed
          break;
        case MenuItemOptionGroupRule.OPTION_MAX:
          if (!isNil(group.maxAmount) && selectedCount > group.maxAmount) {
            throw new BadRequestException(
              `Option group ${group.publishedName} allows maximum ${group.maxAmount} selections`,
            );
          }
          break;
        case MenuItemOptionGroupRule.MANDATORY_FIXED:
          if (selectedCount !== group.fixedAmount) {
            throw new BadRequestException(
              `Option group ${group.publishedName} requires exactly ${group.fixedAmount} selections`,
            );
          }
          break;
        case MenuItemOptionGroupRule.MANDATORY_RANGE:
          if (
            group.fromAmount &&
            group.toAmount &&
            (selectedCount < group.fromAmount || selectedCount > group.toAmount)
          ) {
            throw new BadRequestException(
              `Option group ${group.publishedName} requires between ${group.fromAmount} and ${group.toAmount} selections`,
            );
          }
          break;
      }
    }
  }

  async findCart(cartId: string, user: UserJwtInfo): Promise<Cart> {
    const userId = user.id;

    // 1. Get basic cart data with minimal JOINs
    const cart = await this.getBasicCartData(cartId, userId);

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }

    // 2. Lazy load detailed information
    await Promise.all([
      this.loadMenuItemDetails(cart, false), // Don't need option groups for basic cart
      this.loadCartItemOptions(cart),
      this.loadRestaurantDetails(cart),
    ]);

    // 3. Set price and additional information
    this.getMenuSectionAndItemPrice(cart);

    // 4. Localization
    if (user) {
      localizeCart(cart, user.language);
    }

    return cart;
  }

  async findCartWithMenuItemOptions(cartId: string, user: UserJwtInfo): Promise<Cart> {
    const userId = user.id;

    // 1. Get basic cart data
    const cart = await this.getBasicCartData(cartId, userId);

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }

    // 2. Lazy load all necessary information for validation
    await Promise.all([
      this.loadMenuItemDetails(cart, true), // Load with option groups for validation
      this.loadCartItemOptions(cart),
      this.loadRestaurantDetails(cart),
    ]);

    // 3. Validate active status of all components
    this.validateCartComponentsActiveStatus(cart);

    // 4. Set price with validation
    this.getMenuSectionAndItemPrice(cart, true);

    // 5. Localization
    if (user) {
      localizeCart(cart, user.language);
    }

    return cart;
  }

  private validateCartComponentsActiveStatus(cart: Cart): void {
    // Validate restaurant is active
    if (!cart.restaurant?.activeAt) {
      throw new BadRequestException('Restaurant is not active');
    }

    // Validate brand is active
    if (!cart.restaurant?.brand?.activeAt) {
      throw new BadRequestException('Brand is not active');
    }
  }

  async findActiveCartByRestaurant(user: UserJwtInfo, restaurantId: string): Promise<Cart | undefined> {
    const userId = user.id;

    // 1. Find basic cart by restaurant
    const queryBuilder = this.cartRepository.createQueryBuilder('cart');
    queryBuilder
      .where('cart.restaurantId = :restaurantId', { restaurantId })
      .andWhere('cart.completedAt IS NULL')
      .leftJoinAndSelect('cart.restaurant', 'restaurant')
      .leftJoinAndSelect('cart.cartItems', 'cartItems');

    if (userId) {
      queryBuilder.andWhere('cart.userId = :userId', { userId });
    }

    const cart = await queryBuilder.getOne();
    if (!cart) {
      return undefined;
    }

    // 2. Lazy load detailed information
    await Promise.all([
      this.loadMenuItemDetails(cart),
      this.loadCartItemOptions(cart),
      this.loadRestaurantDetails(cart),
    ]);

    // 3. Set price
    this.getMenuSectionAndItemPrice(cart);

    // 4. Localization
    if (user) {
      localizeCart(cart, user.language);
    }

    return cart;
  }

  async updateCartItemAmount(
    user: UserJwtInfo,
    cartItemId: string,
    updateCartItemAmountDto: UpdateCartItemAmountDto,
  ): Promise<Cart> {
    const userId = user.id;
    const { amount, note, groupOptions } = updateCartItemAmountDto;
    if (!Object.keys(updateCartItemAmountDto).length) {
      throw new BadRequestException('No fields to update');
    }

    const cartItem = await this.cartItemRepository.findOne({
      where: { id: cartItemId, cart: { userId, completedAt: IsNull() } },
      relations: ['cart', 'cartItemOptions'],
    });

    if (!cartItem) {
      throw new NotFoundException('Cart item not found');
    }

    await this.dataSource.transaction(async (manager) => {
      const updatedNote = note !== undefined ? note : cartItem.note;
      const optionsToCompare = groupOptions !== undefined ? groupOptions : [];

      // Find existing cart items with same menuItemId and menuSectionId (excluding current item)
      const existingCartItems = await manager.find(CartItem, {
        where: {
          cartId: cartItem.cartId,
          menuItemId: cartItem.menuItemId,
          menuSectionId: cartItem.menuSectionId,
        },
        relations: ['cartItemOptions'],
        order: { createdAt: 'DESC' },
      });

      // Find a matching cart item with same note and group options (excluding current item)
      let matchingCartItem: CartItem | null = null;
      for (const existingItem of existingCartItems) {
        if (existingItem.id === cartItemId) continue; // Skip current item

        const normalizedExistingNote = existingItem.note?.trim() || '';
        const normalizedUpdatedNote = updatedNote?.trim() || '';

        if (normalizedExistingNote === normalizedUpdatedNote) {
          // If groupOptions is provided, compare them; otherwise, compare existing options with current item's options
          if (this.areGroupOptionsEqual(optionsToCompare, existingItem.cartItemOptions || [])) {
            matchingCartItem = existingItem;
            break;
          }
        }
      }

      if (matchingCartItem) {
        // Merge with existing item
        const updatedAmount = amount !== undefined ? amount : cartItem.amount;
        await manager.update(
          CartItem,
          { id: matchingCartItem.id },
          {
            amount: matchingCartItem.amount + updatedAmount,
          },
        );

        // Remove current cart item and its options
        await manager.softDelete(CartItemOption, { cartItemId });
        await manager.softRemove(cartItem);
        await manager.update(Cart, { id: cartItem.cartId }, { updatedAt: new Date() });

        return;
      }

      // No merging needed, proceed with regular update
      const updateData: Partial<CartItem> = {};
      if (amount !== undefined) updateData.amount = amount;
      if (note !== undefined) updateData.note = note;

      if (Object.keys(updateData).length > 0) {
        await manager.update(CartItem, { id: cartItemId }, updateData);
      }

      // Additionally handle groupOptions
      if (groupOptions) {
        // Get all current options
        const currentOptions = await manager.find(CartItemOption, { where: { cartItemId } });
        // Create a map for comparison
        const currentMap = new Map<string, CartItemOption>();
        for (const opt of currentOptions) {
          currentMap.set(opt.menuItemOptionGroupId + '-' + opt.menuItemOptionId, opt);
        }
        // Create a set of new keys
        const newKeys = new Set<string>();
        for (const group of groupOptions) {
          for (const opt of group.options) {
            const key = group.optionGroupId + '-' + opt.id;
            newKeys.add(key);
            if (currentMap.has(key)) {
              // If exists, update amount if different
              const exist = currentMap.get(key)!;
              if (exist.amount !== (opt.amount || 1)) {
                await manager.update(CartItemOption, { id: exist.id }, { amount: opt.amount || 1 });
              }
            } else {
              // If not exists, add new
              const newOption = manager.create(CartItemOption, {
                cartItemId,
                menuItemOptionGroupId: group.optionGroupId,
                menuItemOptionId: opt.id,
                amount: opt.amount || 1,
              });
              await manager.save(newOption);
            }
          }
        }
        // Delete options that are no longer in the new groupOptions
        for (const [key, exist] of currentMap.entries()) {
          if (!newKeys.has(key)) {
            await manager.softDelete(CartItemOption, { id: exist.id });
          }
        }
      }
      await manager.update(Cart, { id: cartItem.cartId }, { updatedAt: new Date() });
    });
    return this.findCart(cartItem.cartId, user);
  }

  async removeCartItem(user: UserJwtInfo, cartItemId: string): Promise<Cart> {
    const userId = user.id;
    const cartItem = await this.cartItemRepository.findOne({
      where: { id: cartItemId, cart: { userId, completedAt: IsNull() } },
      relations: ['cart'],
    });

    if (!cartItem) {
      throw new NotFoundException('Cart item not found');
    }

    await this.dataSource.transaction(async (manager) => {
      await manager.softRemove(cartItem);
      await manager.softDelete(CartItemOption, { cartItemId: cartItem.id });
      await manager.update(Cart, { id: cartItem.cartId }, { updatedAt: new Date() });
    });
    return this.findCart(cartItem.cartId, user);
  }

  async deleteCart(user: UserJwtInfo, cartId: string) {
    const userId = user.id;
    const cart = await this.cartRepository.findOne({
      where: { id: cartId, userId, completedAt: IsNull() },
      relations: ['cartItems', 'cartItems.cartItemOptions'],
    });

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }
    await this.cartRepository.softRemove(cart);

    return true;
  }

  private areGroupOptionsEqual(options1: CartItemGroupOptionDto[], options2: CartItemOption[]): boolean {
    // Normalize options1 to a comparable format
    const normalizedOptions1 = new Map<string, Map<string, number>>();
    for (const group of options1) {
      const optionsMap = new Map<string, number>();
      for (const option of group.options) {
        optionsMap.set(option.id, option.amount || 1);
      }
      normalizedOptions1.set(group.optionGroupId, optionsMap);
    }

    // Normalize options2 to a comparable format
    const normalizedOptions2 = new Map<string, Map<string, number>>();
    for (const option of options2) {
      if (!normalizedOptions2.has(option.menuItemOptionGroupId)) {
        normalizedOptions2.set(option.menuItemOptionGroupId, new Map<string, number>());
      }
      normalizedOptions2.get(option.menuItemOptionGroupId)!.set(option.menuItemOptionId, option.amount);
    }

    // Compare the two normalized structures
    if (normalizedOptions1.size !== normalizedOptions2.size) {
      return false;
    }

    for (const [groupId, options1Map] of normalizedOptions1) {
      const options2Map = normalizedOptions2.get(groupId);
      if (!options2Map || options1Map.size !== options2Map.size) {
        return false;
      }

      for (const [optionId, amount1] of options1Map) {
        const amount2 = options2Map.get(optionId);
        if (amount2 === undefined || amount1 !== amount2) {
          return false;
        }
      }
    }

    return true;
  }

  async addToCart(user: UserJwtInfo, addToCartDto: AddToCartDto): Promise<Cart> {
    const userId = user.id;
    const { restaurantId, menuItemId, menuSectionId, amount, groupOptions, note } = addToCartDto;

    const menuItem = await this.menuItemsService.findOneByCartWithRelations(restaurantId, menuSectionId, menuItemId);

    // Validate menu item belongs to restaurant
    this.validateMenuItemBelongsToRestaurant(menuItem, restaurantId);

    // Validate menu item availability
    this.validateMenuItemAvailability(menuItem);

    // Validate menu item options
    this.validateMenuItemOptions(menuItem, groupOptions);

    return this.dataSource.transaction(async (manager) => {
      let cart = await manager.findOne(Cart, {
        where: { userId, restaurantId, completedAt: IsNull() },
      });

      if (!cart) {
        cart = manager.create(Cart, { userId, restaurantId } as Cart);
        cart = await manager.save(cart);
      }

      // Check for existing cart item with same properties
      // Merge if notes are the same (including both empty/null)
      let matchingCartItem: CartItem | null = null;

      const existingCartItems = await manager.find(CartItem, {
        where: {
          cartId: cart.id,
          menuItemId,
          menuSectionId,
        },
        relations: ['cartItemOptions'],
        order: { createdAt: 'DESC' },
      });

      // Find a matching cart item with same group options and same note
      for (const existingItem of existingCartItems) {
        const normalizedExistingNote = existingItem.note?.trim() || '';
        const normalizedNewNote = note?.trim() || '';

        if (
          normalizedExistingNote === normalizedNewNote &&
          this.areGroupOptionsEqual(groupOptions, existingItem.cartItemOptions || [])
        ) {
          matchingCartItem = existingItem;
          break;
        }
      }

      if (matchingCartItem) {
        // Update existing cart item amount
        await manager.update(CartItem, { id: matchingCartItem.id }, { amount: matchingCartItem.amount + amount });
      } else {
        // Create new cart item
        const cartItem = manager.create(CartItem, {
          cartId: cart.id,
          menuItemId,
          menuSectionId,
          amount,
          note,
        } as CartItem);

        const savedCartItem = await manager.save(cartItem);

        const cartItemOptions: CartItemOption[] = [];

        for (const option of groupOptions) {
          const optionAmountMap = new Map<string, number>();

          if (option.options && option.options.length > 0) {
            for (const optWithAmount of option.options) {
              optionAmountMap.set(optWithAmount.id, optWithAmount.amount || 1);
            }
          }

          for (const optionId of option.options.map((opt) => opt.id)) {
            const amount = optionAmountMap.has(optionId) ? optionAmountMap.get(optionId) : 1;

            const cartItemOption = manager.create(CartItemOption, {
              cartItemId: savedCartItem.id,
              menuItemOptionGroupId: option.optionGroupId,
              menuItemOptionId: optionId,
              amount: amount,
            } as CartItemOption);

            cartItemOptions.push(cartItemOption);
          }
        }

        if (cartItemOptions.length > 0) {
          await manager.save(cartItemOptions);
        }
      }

      await manager.update(Cart, { id: cart.id }, { updatedAt: new Date() });

      const cartResult = await manager.findOne(Cart, {
        where: { id: cart.id },
        relations: ['cartItems', 'cartItems.cartItemOptions'],
      });

      if (!cartResult) {
        throw new NotFoundException('Cart not found');
      }

      // Add localization after transaction
      if (user) {
        localizeCart(cartResult, user.language);
      }

      return cartResult;
    });
  }

  async getYouMightAlsoLike(user: UserJwtInfo, restaurantId: string): Promise<MenuItem[]> {
    const existingCart = await this.cartRepository.findOne({
      where: { userId: user.id, restaurantId, completedAt: IsNull() },
      relations: ['cartItems'],
    });
    const menuItemsIds: string[] = Array.from(
      new Set(existingCart ? existingCart.cartItems.map((item) => item.menuItemId) : []),
    );

    const allEligibleItems = await this.menuItemsService.getListByRestaurantIdExcludingCartItemsWithValidation(
      restaurantId,
      menuItemsIds,
      user.language,
    );

    const shuffled = allEligibleItems.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 10);
  }

  async findAll(user: UserJwtInfo, query: CartQueryDto): Promise<Pagination<Cart>> {
    const { page, limit, restaurantId } = query;
    const userId = user.id;

    // 1. Get basic cart list with pagination
    const queryBuilder = this.cartRepository.createQueryBuilder('cart');

    queryBuilder
      .leftJoinAndSelect('cart.restaurant', 'restaurant')
      .leftJoinAndSelect('cart.cartItems', 'cartItems')
      .andWhere('cart.completedAt IS NULL');

    if (userId) {
      queryBuilder.andWhere('cart.userId = :userId', { userId });
    }

    if (restaurantId) {
      queryBuilder.andWhere('cart.restaurantId = :restaurantId', { restaurantId });
    }

    queryBuilder.orderBy('cart.updatedAt', 'DESC');

    const carts = await paginateQueryBuilder<Cart>(queryBuilder, { page, limit });

    if (carts.items.length > 0) {
      // 2. Batch load all details at once instead of per cart
      await this.batchLoadCartDetails(carts.items);
    }

    // 3. Process each cart sequentially (not resource intensive)
    for (const cart of carts.items) {
      this.getMenuSectionAndItemPrice(cart);
      localizeCart(cart, user.language);
    }

    return carts;
  }

  /**
   * Batch load all detailed information for multiple carts at once
   * Reduce from N*2 queries to only 2 queries for the entire list
   */
  private async batchLoadCartDetails(carts: Cart[]): Promise<void> {
    if (carts.length === 0) return;

    // Collect all necessary IDs
    const allMenuItemIds: string[] = [];
    const allCartItemIds: string[] = [];

    for (const cart of carts) {
      if (cart.cartItems) {
        for (const cartItem of cart.cartItems) {
          allMenuItemIds.push(cartItem.menuItemId);
          allCartItemIds.push(cartItem.id);
        }
      }
    }

    if (allMenuItemIds.length === 0) return;

    // Batch load all menu items and cart options in parallel
    const [allMenuItems, allCartItemOptions] = await Promise.all([
      this.batchLoadMenuItems(allMenuItemIds),
      this.batchLoadCartItemOptions(allCartItemIds),
    ]);

    // Map data into each cart
    for (const cart of carts) {
      if (!cart.cartItems) continue;

      for (const cartItem of cart.cartItems) {
        // Map menu item
        const menuItem = allMenuItems.find((item) => item.id === cartItem.menuItemId);
        if (menuItem) {
          cartItem.menuItem = menuItem;
        }

        // Map cart item options
        cartItem.cartItemOptions = allCartItemOptions.filter((option) => option.cartItemId === cartItem.id);
      }
    }
  }

  /**
   * Batch load menu items for multiple carts
   */
  private async batchLoadMenuItems(menuItemIds: string[]): Promise<MenuItem[]> {
    if (menuItemIds.length === 0) return [];

    const uniqueMenuItemIds = [...new Set(menuItemIds)];

    return this.dataSource
      .getRepository(MenuItem)
      .createQueryBuilder('menuItem')
      .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection', 'menuSection.activeAt IS NOT NULL')
      .leftJoinAndSelect('menuSection.mappingMenus', 'mappingMenus')
      .leftJoinAndSelect('mappingMenus.menu', 'menu', 'menu.activeAt IS NOT NULL')
      .where('menuItem.id IN (:...menuItemIds)', { menuItemIds: uniqueMenuItemIds })
      .andWhere('menuItem.activeAt IS NOT NULL')
      .getMany();
  }

  /**
   * Batch load cart item options for multiple carts
   */
  private async batchLoadCartItemOptions(cartItemIds: string[]): Promise<CartItemOption[]> {
    if (cartItemIds.length === 0) return [];

    const uniqueCartItemIds = [...new Set(cartItemIds)];

    return this.dataSource
      .getRepository(CartItemOption)
      .createQueryBuilder('cartItemOption')
      .leftJoinAndSelect('cartItemOption.menuItemOption', 'menuItemOption', 'menuItemOption.activeAt IS NOT NULL')
      .leftJoinAndSelect(
        'menuItemOption.mappingMenuItemOptionGroupMenuItemOption',
        'mappingMenuItemOptionGroupMenuItemOption',
      )
      .leftJoinAndSelect('mappingMenuItemOptionGroupMenuItemOption.menuItemOptionGroup', 'menuItemOptionGroup')
      .where('cartItemOption.cartItemId IN (:...cartItemIds)', { cartItemIds: uniqueCartItemIds })
      .getMany();
  }

  /**
   * Get basic cart data with as few JOINs as possible
   */
  private async getBasicCartData(cartId: string, userId?: string): Promise<Cart | null> {
    const queryBuilder = this.cartRepository.createQueryBuilder('cart');

    queryBuilder
      .where('cart.id = :cartId', { cartId })
      .leftJoinAndSelect('cart.restaurant', 'restaurant')
      .leftJoinAndSelect('cart.cartItems', 'cartItems')
      .andWhere('cart.completedAt IS NULL');

    if (userId) {
      queryBuilder.andWhere('cart.userId = :userId', { userId });
    }

    return queryBuilder.getOne();
  }

  /**
   * Load detailed information of menu items for cart
   * @param cart Cart object to load data into
   * @param loadOptionGroups Whether to load menu item option groups for validation
   */
  private async loadMenuItemDetails(cart: Cart, loadOptionGroups: boolean = false): Promise<void> {
    if (!cart.cartItems || cart.cartItems.length === 0) return;

    const menuItemIds = cart.cartItems.map((item) => item.menuItemId);

    // Load menu items and option groups in parallel
    const [menuItems, menuItemsWithOptions] = await Promise.all([
      // Get menu items with necessary information
      this.dataSource
        .getRepository(MenuItem)
        .createQueryBuilder('menuItem')
        .leftJoinAndSelect('menuItem.mappingMenuSections', 'mappingMenuSections')
        .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection', 'menuSection.activeAt IS NOT NULL')
        .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
        .leftJoinAndSelect('menuSection.mappingMenus', 'mappingMenus')
        .leftJoinAndSelect('mappingMenus.menu', 'menu', 'menu.activeAt IS NOT NULL')
        .where('menuItem.id IN (:...menuItemIds)', { menuItemIds })
        .andWhere('menuItem.activeAt IS NOT NULL')
        .getMany(),

      // Get menu item option groups if needed
      loadOptionGroups
        ? this.dataSource
            .getRepository(MenuItem)
            .createQueryBuilder('menuItem')
            .leftJoinAndSelect('menuItem.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups')
            .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup')
            .leftJoinAndSelect('menuItemOptionGroup.mappingMenuItemOptions', 'mappingMenuItemOptions')
            .leftJoinAndSelect(
              'mappingMenuItemOptions.menuItemOption',
              'menuItemOption',
              'menuItemOption.activeAt IS NOT NULL',
            )
            .where('menuItem.id IN (:...menuItemIds)', { menuItemIds })
            .getMany()
        : Promise.resolve([]),
    ]);

    // Step 1: Map menu items into cart items first
    for (const cartItem of cart.cartItems) {
      const menuItem = menuItems.find((item) => item.id === cartItem.menuItemId);
      if (menuItem) {
        cartItem.menuItem = menuItem;
        // Note: Price và menuSection sẽ được set trong getMenuSectionAndItemPrice()
      }
    }

    // Step 2: Map option groups into menu items (only after menu items are mapped)
    if (loadOptionGroups && menuItemsWithOptions.length > 0) {
      for (const cartItem of cart.cartItems) {
        const menuItemWithOptions = menuItemsWithOptions.find((item) => item.id === cartItem.menuItemId);
        if (menuItemWithOptions && cartItem.menuItem) {
          Object.assign(cartItem.menuItem, {
            mappingMenuItemOptionGroups: menuItemWithOptions.mappingMenuItemOptionGroups,
          });
        }
      }
    }
  }

  /**
   * Load cart item options information
   */
  private async loadCartItemOptions(cart: Cart): Promise<void> {
    if (!cart.cartItems || cart.cartItems.length === 0) return;

    const cartItemIds = cart.cartItems.map((item) => item.id);

    // Get cart item options with option information
    const cartItemOptions = await this.dataSource
      .getRepository(CartItemOption)
      .createQueryBuilder('cartItemOption')
      .leftJoinAndSelect('cartItemOption.menuItemOption', 'menuItemOption', 'menuItemOption.activeAt IS NOT NULL')
      .leftJoinAndSelect(
        'menuItemOption.mappingMenuItemOptionGroupMenuItemOption',
        'mappingMenuItemOptionGroupMenuItemOption',
      )
      .leftJoinAndSelect('mappingMenuItemOptionGroupMenuItemOption.menuItemOptionGroup', 'menuItemOptionGroup')
      .where('cartItemOption.cartItemId IN (:...cartItemIds)', { cartItemIds })
      .getMany();

    // Map options into cart items
    for (const cartItem of cart.cartItems) {
      cartItem.cartItemOptions = cartItemOptions.filter((option) => option.cartItemId === cartItem.id);
      // Note: Price và menuItemOptionGroup sẽ được set trong getMenuSectionAndItemPrice()
    }
  }

  /**
   * Load restaurant details information when needed (brand, merchant account)
   */
  private async loadRestaurantDetails(cart: Cart): Promise<void> {
    if (!cart.restaurant) return;

    const restaurant = await this.dataSource
      .getRepository('restaurants')
      .createQueryBuilder('restaurant')
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule')
      .where('restaurant.id = :restaurantId', { restaurantId: cart.restaurant.id })
      .getOne();

    if (restaurant) {
      Object.assign(cart.restaurant, restaurant);
    }
  }

  private getMenuSectionAndItemPrice(cart: Cart, validateExist: boolean = false) {
    if (!cart.cartItems) return;
    for (const cartItem of cart.cartItems) {
      if (!cartItem.menuItem) {
        if (validateExist) throw new NotFoundException('Some dishes are currently unavailable');
        continue;
      }

      if (validateExist && !cartItem.menuItem.activeAt) {
        if (validateExist) throw new NotFoundException('Some dishes are currently unavailable');
        continue;
      }

      const menuSection = cartItem.menuItem.menuSections?.find((section) => section.id === cartItem.menuSectionId);
      if (validateExist && (!menuSection || menuSection.menus?.length === 0))
        throw new NotFoundException('Some dishes in certain section is currently inactive.');
      cartItem.menuItem.price = menuSection?.itemPrice;
      cartItem.menuSection = menuSection;
      if (!cartItem.cartItemOptions?.length) continue;
      for (const cartItemOption of cartItem.cartItemOptions) {
        if (!cartItemOption.menuItemOption) {
          if (validateExist) throw new NotFoundException('Some options in certain items are currently unavailable.');
          continue;
        }
        const menuItemOption = cartItemOption.menuItemOption;
        const menuItemOptionGroup = menuItemOption.optionOfMenuItemOptionGroups?.find(
          (m) => m.id === cartItemOption.menuItemOptionGroupId,
        );
        if (!menuItemOptionGroup && validateExist) throw new NotFoundException('Have menu item option group not found');
        menuItemOption.price = menuItemOptionGroup?.optionPrice;
        cartItemOption.menuItemOptionGroup = menuItemOptionGroup;
      }
    }
  }
}
