import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateRestaurantReviewReplyDto } from '../dto/create-restaurant-review-reply.dto';
import { ListStaffRestaurantReviewDto } from '../dto/list-staff-restaurant-review.dto';
import { RestaurantReviewReply } from '../entities/restaurant-review-reply.entity';
import { RestaurantReview } from '../entities/restaurant-review.entity';
import { RestaurantReviewsService } from '../restaurant-reviews.service';

@ApiTags('(Merchant User) Restaurant Reviews')
@Controller('merchant-user/restaurants/reviews')
@Roles({ userType: UserType.MERCHANT_USER })
export class MerchantUserRestaurantReviewsController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN, MerchantUserRole.MANAGER];
  constructor(private readonly restaurantReviewsService: RestaurantReviewsService) {}

  @Post('reply')
  @ApiOperation({ summary: 'Reply to a restaurant review' })
  async createRestaurantReviewReplyByMerchantUser(
    @Body() createReplyDto: CreateRestaurantReviewReplyDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<RestaurantReviewReply> {
    return this.restaurantReviewsService.createRestaurantReviewReplyByMerchantUser(
      createReplyDto,
      user,
      this.permissionRoles,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get restaurant reviews for staff' })
  async findRestaurantReviewsForMerchantUser(
    @Query() query: ListStaffRestaurantReviewDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<Pagination<RestaurantReview>> {
    return this.restaurantReviewsService.findRestaurantReviewsForMerchantUser(query, user, this.permissionRoles);
  }

  @Get('stats/:restaurantId')
  @ApiOperation({ summary: 'Get restaurant review statistics' })
  async getRestaurantReviewStats(@Param('restaurantId', ParseUUIDPipe) restaurantId: string) {
    return this.restaurantReviewsService.getRestaurantReviewStats(restaurantId);
  }
}
