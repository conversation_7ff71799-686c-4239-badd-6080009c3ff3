import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { OptionalAuth } from '@/modules/auth/decorators/optional-auth.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateRestaurantReviewReplyDto } from '../dto/create-restaurant-review-reply.dto';
import { CreateRestaurantReviewDto } from '../dto/create-restaurant-review.dto';
import { ListRestaurantReviewDto } from '../dto/list-restaurant-review.dto';
import { RestaurantReviewReply } from '../entities/restaurant-review-reply.entity';
import { RestaurantReview } from '../entities/restaurant-review.entity';
import { RestaurantReviewsService } from '../restaurant-reviews.service';

@ApiTags('(User) Restaurant Reviews')
@Controller('user/restaurants/reviews')
@Roles({ userType: UserType.USER, role: '*' })
export class UserRestaurantReviewsController {
  constructor(private readonly restaurantReviewsService: RestaurantReviewsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a restaurant review' })
  async createRestaurantReview(
    @User('id') userId: string,
    @Body() createReviewDto: CreateRestaurantReviewDto,
  ): Promise<RestaurantReview> {
    return this.restaurantReviewsService.createRestaurantReview(userId, createReviewDto);
  }

  @Post('reply')
  @ApiOperation({ summary: 'Reply to a restaurant review' })
  async createRestaurantReviewReplyByUser(
    @User('id') userId: string,
    @Body() createReplyDto: CreateRestaurantReviewReplyDto,
  ): Promise<RestaurantReviewReply> {
    return this.restaurantReviewsService.createRestaurantReviewReplyByUser(userId, createReplyDto);
  }

  @OptionalAuth()
  @Get('stats/:restaurantId')
  @ApiOperation({ summary: 'Get restaurant review statistics' })
  async getRestaurantReviewStats(@Param('restaurantId', ParseUUIDPipe) restaurantId: string) {
    return this.restaurantReviewsService.getRestaurantReviewStats(restaurantId);
  }

  @OptionalAuth()
  @Get('list/:restaurantId')
  @ApiOperation({ summary: 'Get restaurant reviews' })
  async getRestaurantReviewList(
    @Param('restaurantId', ParseUUIDPipe) restaurantId: string,
    @Query() query: ListRestaurantReviewDto,
  ): Promise<Pagination<RestaurantReview>> {
    return this.restaurantReviewsService.findRestaurantReviewList(restaurantId, query);
  }
}
