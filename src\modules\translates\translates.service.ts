import { In, Repository } from 'typeorm';

import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MenuItemOptionGroup } from '../menu-item-option-groups/entities/menu-item-option-group.entity';
import { MenuItem } from '../menu-items/entities/menu-item.entity';
import { MenuSection } from '../menu-sections/entities/menu-section.entity';
import { GetTranslatesDto, TranslateType } from './dtos/get-translates.dto';
import { UpdateTranslatesDto } from './dtos/update-translates.dto';

export interface TranslateResponseItem {
  id: string;
  type: TranslateType;
  publishedName: string;
  publishedNameEn?: string | null;
  publishedNameVi?: string | null;
  description?: string | null;
  descriptionEn?: string | null;
  descriptionVi?: string | null;
}

@Injectable()
export class TranslatesService {
  constructor(
    @InjectRepository(MenuSection)
    private readonly menuSectionRepository: Repository<MenuSection>,
    @InjectRepository(MenuItem)
    private readonly menuItemRepository: Repository<MenuItem>,
    @InjectRepository(MenuItemOptionGroup)
    private readonly menuItemOptionGroupRepository: Repository<MenuItemOptionGroup>,
    private readonly permissionMerchantUserService: PermissionMerchantUserService,
  ) {}

  async getTranslates(
    dto: GetTranslatesDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<TranslateResponseItem[]> {
    // Verify restaurant access
    await this.permissionMerchantUserService.verifyAccessRestaurant(dto.restaurantId, user, permissionRoles);

    const results: TranslateResponseItem[] = [];

    // Get menu sections
    if (!dto.type || dto.type === TranslateType.MENU_SECTION) {
      const menuSectionsQB = this.menuSectionRepository
        .createQueryBuilder('section')
        .where('section.restaurantId = :restaurantId', { restaurantId: dto.restaurantId })
        .select(['section.id', 'section.publishedName', 'section.publishedNameEn', 'section.publishedNameVi']);

      if (dto.menuId) {
        menuSectionsQB
          .leftJoin('section.mappingMenus', 'mappingMenus')
          .leftJoin('mappingMenus.menu', 'menu')
          .andWhere('menu.id = :menuId', { menuId: dto.menuId });
      }

      const menuSections = await menuSectionsQB.orderBy('section.createdAt', 'DESC').getMany();
      results.push(
        ...menuSections.map((section) => ({
          id: section.id,
          type: TranslateType.MENU_SECTION,
          publishedName: section.publishedName,
          publishedNameEn: section.publishedNameEn,
          publishedNameVi: section.publishedNameVi,
        })),
      );
    }

    // Get menu items
    if (!dto.type || dto.type === TranslateType.MENU_ITEM) {
      const menuItemsQB = this.menuItemRepository
        .createQueryBuilder('menuItem')
        .where('menuItem.restaurantId = :restaurantId', { restaurantId: dto.restaurantId });
      if (dto.menuId) {
        menuItemsQB
          .leftJoin('menuItem.mappingMenuItemOptionGroupMenuItemOption', 'mappingMenuItemOptionGroupMenuItemOption')
          .leftJoin('mappingMenuItemOptionGroupMenuItemOption.menuItemOptionGroup', 'menuItemOptionGroup')
          .leftJoin('menuItemOptionGroup.mappingMenuItemMenus', 'mappingMenuItemMenus')
          .leftJoin('mappingMenuItemMenus.menuItem', 'menuItem2')
          .leftJoin('menuItem2.mappingMenuSections', 'mappingMenuSections')
          .leftJoin('mappingMenuSections.menuSection', 'menuSection')
          .leftJoin('menuSection.mappingMenus', 'mappingMenus')
          .leftJoin('mappingMenus.menu', 'menu')

          .leftJoin('menuItem.mappingMenuSections', 'mappingMenuSections2')
          .leftJoin('mappingMenuSections2.menuSection', 'menuSection2')
          .leftJoin('menuSection2.mappingMenus', 'mappingMenus2')
          .leftJoin('mappingMenus2.menu', 'menu2')
          .andWhere('(menu.id = :menuId OR menu2.id = :menuId)', { menuId: dto.menuId });
      }
      menuItemsQB.select([
        'menuItem.id',
        'menuItem.publishedName',
        'menuItem.publishedNameEn',
        'menuItem.publishedNameVi',
        'menuItem.description',
        'menuItem.descriptionEn',
        'menuItem.descriptionVi',
      ]);
      const menuItems = await menuItemsQB.orderBy('menuItem.createdAt', 'DESC').getMany();
      results.push(
        ...menuItems.map((item) => ({
          id: item.id,
          type: TranslateType.MENU_ITEM,
          publishedName: item.publishedName,
          publishedNameEn: item.publishedNameEn,
          publishedNameVi: item.publishedNameVi,
          description: item.description,
          descriptionEn: item.descriptionEn,
          descriptionVi: item.descriptionVi,
        })),
      );
    }

    // Get menu item option groups
    if (!dto.type || dto.type === TranslateType.MENU_ITEM_OPTION_GROUP) {
      const optionGroupsQB = this.menuItemOptionGroupRepository
        .createQueryBuilder('group')
        .where('group.restaurantId = :restaurantId', { restaurantId: dto.restaurantId })
        .select(['group.id', 'group.publishedName', 'group.publishedNameEn', 'group.publishedNameVi']);
      if (dto.menuId) {
        optionGroupsQB
          .leftJoin('group.mappingMenuItemMenus', 'mappingMenuItemMenus')
          .leftJoin('mappingMenuItemMenus.menuItem', 'menuItem')
          .leftJoin('menuItem.mappingMenuSections', 'mappingMenuSections')
          .leftJoin('mappingMenuSections.menuSection', 'menuSection')
          .leftJoin('menuSection.mappingMenus', 'mappingMenus')
          .leftJoin('mappingMenus.menu', 'menu')
          .andWhere('menu.id = :menuId', { menuId: dto.menuId });
      }
      const optionGroups = await optionGroupsQB.orderBy('group.createdAt', 'DESC').getMany();
      results.push(
        ...optionGroups.map((group) => ({
          id: group.id,
          type: TranslateType.MENU_ITEM_OPTION_GROUP,
          publishedName: group.publishedName,
          publishedNameEn: group.publishedNameEn,
          publishedNameVi: group.publishedNameVi,
        })),
      );
    }

    return results;
  }

  async updateTranslates(
    dto: UpdateTranslatesDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<{ updated: number }> {
    const { restaurantId, items } = dto;
    // Verify restaurant access
    await this.permissionMerchantUserService.verifyAccessRestaurant(restaurantId, user, permissionRoles);

    // Group items by type for batch processing
    const menuSectionUpdates: Array<{ id: string; data: any }> = [];
    const menuItemUpdates: Array<{ id: string; data: any }> = [];
    const menuItemOptionGroupUpdates: Array<{ id: string; data: any }> = [];

    // Helper function to remove undefined values
    const cleanUpdateData = (data: any): any => {
      const cleaned = {};
      Object.keys(data).forEach((key) => {
        if (data[key] !== undefined) {
          cleaned[key] = data[key];
        }
      });
      return cleaned;
    };

    // Process and group items by type
    for (const item of items) {
      let updateData: any = {};

      switch (item.type) {
        case TranslateType.MENU_SECTION:
          updateData = cleanUpdateData({
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
          });
          if (Object.keys(updateData).length > 0) {
            menuSectionUpdates.push({ id: item.id, data: updateData });
          }
          break;

        case TranslateType.MENU_ITEM:
          updateData = cleanUpdateData({
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
            description: item.description,
            descriptionEn: item.descriptionEn,
            descriptionVi: item.descriptionVi,
          });
          if (Object.keys(updateData).length > 0) {
            menuItemUpdates.push({ id: item.id, data: updateData });
          }
          break;

        case TranslateType.MENU_ITEM_OPTION_GROUP:
          updateData = cleanUpdateData({
            publishedName: item.publishedName,
            publishedNameEn: item.publishedNameEn,
            publishedNameVi: item.publishedNameVi,
          });
          if (Object.keys(updateData).length > 0) {
            menuItemOptionGroupUpdates.push({ id: item.id, data: updateData });
          }
          break;
      }
    }

    let updatedCount = 0;

    // Batch update menu sections
    if (menuSectionUpdates.length > 0) {
      await this.batchUpdateEntities(
        this.menuSectionRepository,
        menuSectionUpdates,
        restaurantId,
        TranslateType.MENU_SECTION,
      );
      updatedCount += menuSectionUpdates.length;
    }

    // Batch update menu items
    if (menuItemUpdates.length > 0) {
      await this.batchUpdateEntities(this.menuItemRepository, menuItemUpdates, restaurantId, TranslateType.MENU_ITEM);
      updatedCount += menuItemUpdates.length;
    }

    // Batch update menu item option groups
    if (menuItemOptionGroupUpdates.length > 0) {
      await this.batchUpdateEntities(
        this.menuItemOptionGroupRepository,
        menuItemOptionGroupUpdates,
        restaurantId,
        TranslateType.MENU_ITEM_OPTION_GROUP,
      );
      updatedCount += menuItemOptionGroupUpdates.length;
    }

    return { updated: updatedCount };
  }

  private async batchUpdateEntities(
    repository: Repository<any>,
    updates: Array<{ id: string; data: any }>,
    restaurantId: string,
    entityType: TranslateType,
  ): Promise<void> {
    // Get all entities to verify they exist and check restaurant access
    const entityIds = updates.map((update) => update.id);
    const entities = await repository.find({
      where: { id: In(entityIds), restaurantId },
      select: ['id', 'restaurantId'],
    });

    if (entities.length !== entityIds.length) {
      const foundIds = entities.map((entity) => entity.id);
      const missingIds = entityIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(`Entities with ids ${missingIds.join(', ')} and type ${entityType} not found`);
    }

    // Perform batch updates
    const updatePromises = updates.map((update) => repository.update(update.id, update.data));
    await Promise.all(updatePromises);
  }
}
