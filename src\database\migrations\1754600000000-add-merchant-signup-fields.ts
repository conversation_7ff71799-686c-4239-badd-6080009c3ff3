import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMerchantSignupFields1754600000000 implements MigrationInterface {
  name = 'AddMerchantSignupFields1754600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add phone field to merchant_users table
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "phone" character varying`);
    await queryRunner.query(`CREATE INDEX "IDX_merchant_users_phone" ON "merchant_users" ("phone") WHERE deleted_at IS NULL`);

    // Create business_type enum
    await queryRunner.query(`CREATE TYPE "public"."brands_business_type_enum" AS ENUM('Company', 'Sole Proprietor', 'Individually Owned')`);

    // Add new fields to brands table
    await queryRunner.query(`ALTER TABLE "brands" ADD "business_type" "public"."brands_business_type_enum"`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "registered_name" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "registration_number" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_address" text`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_ward" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_district" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_province" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_latitude" numeric(10,8)`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_longitude" numeric(11,8)`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_location" geography(Point,4326)`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "company_tax_code" character varying`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove new fields from brands table
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "company_tax_code"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_location"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_longitude"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_latitude"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_province"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_district"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_ward"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_address"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "registration_number"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "registered_name"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "business_type"`);

    // Drop business_type enum
    await queryRunner.query(`DROP TYPE "public"."brands_business_type_enum"`);

    // Remove phone field from merchant_users table
    await queryRunner.query(`DROP INDEX "public"."IDX_merchant_users_phone"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "phone"`);
  }
}
